package secretsmanager

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"go.uber.org/zap"
)

// SecretsClient interface for retrieving secrets
type SecretsClient interface {
	GetJDCredentials(ctx context.Context) (*JDCredentials, error)
	GetFullConfiguration(ctx context.Context) (*FullConfiguration, error)
}

// JDCredentials represents JD API credentials
type JDCredentials struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
}

// FullConfiguration represents all configuration that can be stored in SecretManager
type FullConfiguration struct {
	Server  *ServerConfiguration  `json:"server,omitempty"`
	JD      *JDConfiguration      `json:"jd,omitempty"`
	AWS     *AWSConfiguration     `json:"aws,omitempty"`
	Secrets *SecretsConfiguration `json:"secrets,omitempty"`
	Log     *LogConfiguration     `json:"log,omitempty"`
}

// ServerConfiguration server configuration from SecretManager
type ServerConfiguration struct {
	Host string `json:"host,omitempty"`
	Port int    `json:"port,omitempty"`
}

// JDConfiguration JD API configuration from SecretManager
type JDConfiguration struct {
	BaseURL      string `json:"base_url,omitempty"`
	GrantType    string `json:"grant_type,omitempty"`
	Scope        string `json:"scope,omitempty"`
	ClientID     string `json:"client_id,omitempty"`
	ClientSecret string `json:"client_secret,omitempty"`
	CacheMode    *bool  `json:"cache_mode,omitempty"`
	MockMode     *bool  `json:"mock_mode,omitempty"`
	TimeoutSecs  int    `json:"timeout_seconds,omitempty"`
}

// AWSConfiguration AWS configuration from SecretManager
type AWSConfiguration struct {
	Region string `json:"region,omitempty"`
}

// SecretsConfiguration secrets configuration from SecretManager
type SecretsConfiguration struct {
	SecretName string `json:"secret_name,omitempty"`
}

// LogConfiguration logging configuration from SecretManager
type LogConfiguration struct {
	Level            string `json:"level,omitempty"`
	Format           string `json:"format,omitempty"`
	OutputPath       string `json:"output_path,omitempty"`
	EnableCaller     *bool  `json:"enable_caller,omitempty"`
	EnableStacktrace *bool  `json:"enable_stacktrace,omitempty"`
	TimeFormat       string `json:"time_format,omitempty"`
}

// AWSSecretsManagerClient AWS Secrets Manager client implementation
type AWSSecretsManagerClient struct {
	client     *secretsmanager.Client
	secretName string
	region     string
	logger     *zap.Logger
}

// NewAWSSecretsManagerClient creates a new AWS Secrets Manager client
func NewAWSSecretsManagerClient(region, secretName string, logger *zap.Logger) (*AWSSecretsManagerClient, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	return &AWSSecretsManagerClient{
		client:     secretsmanager.NewFromConfig(cfg),
		secretName: secretName,
		region:     region,
		logger:     logger,
	}, nil
}

// GetJDCredentials retrieves JD credentials from AWS Secrets Manager
func (c *AWSSecretsManagerClient) GetJDCredentials(ctx context.Context) (*JDCredentials, error) {
	c.logger.Info("Retrieving JD credentials from AWS Secrets Manager",
		zap.String("secret_name", c.secretName),
		zap.String("region", c.region),
	)

	input := &secretsmanager.GetSecretValueInput{
		SecretId: &c.secretName,
	}

	result, err := c.client.GetSecretValue(ctx, input)
	if err != nil {
		c.logger.Error("Failed to retrieve secret from AWS Secrets Manager",
			zap.String("secret_name", c.secretName),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get secret from AWS Secrets Manager: %w", err)
	}

	if result.SecretString == nil {
		return nil, fmt.Errorf("secret string is nil")
	}

	var credentials JDCredentials
	if err := json.Unmarshal([]byte(*result.SecretString), &credentials); err != nil {
		c.logger.Error("Failed to parse JD credentials from secret",
			zap.String("secret_name", c.secretName),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to parse JD credentials: %w", err)
	}

	c.logger.Info("Successfully retrieved JD credentials from AWS Secrets Manager",
		zap.String("client_id", maskString(credentials.ClientID)),
	)

	return &credentials, nil
}

// GetFullConfiguration retrieves full configuration from AWS Secrets Manager
func (c *AWSSecretsManagerClient) GetFullConfiguration(ctx context.Context) (*FullConfiguration, error) {
	c.logger.Info("Retrieving full configuration from AWS Secrets Manager",
		zap.String("secret_name", c.secretName),
		zap.String("region", c.region),
	)

	input := &secretsmanager.GetSecretValueInput{
		SecretId: &c.secretName,
	}

	result, err := c.client.GetSecretValue(ctx, input)
	if err != nil {
		c.logger.Error("Failed to retrieve secret from AWS Secrets Manager",
			zap.String("secret_name", c.secretName),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get secret from AWS Secrets Manager: %w", err)
	}

	if result.SecretString == nil {
		return nil, fmt.Errorf("secret string is nil")
	}

	var fullConfig FullConfiguration
	if err := json.Unmarshal([]byte(*result.SecretString), &fullConfig); err != nil {
		c.logger.Error("Failed to parse full configuration from secret",
			zap.String("secret_name", c.secretName),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to parse full configuration: %w", err)
	}

	c.logger.Info("Successfully retrieved full configuration from AWS Secrets Manager")
	return &fullConfig, nil
}

// ConfigFileSecretsClient configuration file-based secrets client
type ConfigFileSecretsClient struct {
	clientID     string
	clientSecret string
	logger       *zap.Logger
}

// NewConfigFileSecretsClient creates a new configuration file-based secrets client
func NewConfigFileSecretsClient(clientID, clientSecret string, logger *zap.Logger) *ConfigFileSecretsClient {
	return &ConfigFileSecretsClient{
		clientID:     clientID,
		clientSecret: clientSecret,
		logger:       logger,
	}
}

// GetJDCredentials retrieves JD credentials from configuration
func (c *ConfigFileSecretsClient) GetJDCredentials(ctx context.Context) (*JDCredentials, error) {
	c.logger.Info("Using JD credentials from configuration file",
		zap.String("client_id", maskString(c.clientID)),
	)

	return &JDCredentials{
		ClientID:     c.clientID,
		ClientSecret: c.clientSecret,
	}, nil
}

// GetFullConfiguration returns empty configuration (config file client doesn't provide full config)
func (c *ConfigFileSecretsClient) GetFullConfiguration(ctx context.Context) (*FullConfiguration, error) {
	c.logger.Info("Config file client does not provide full configuration")
	return &FullConfiguration{}, nil
}

// DualModeSecretsClient supports both AWS Secrets Manager and config file fallback
type DualModeSecretsClient struct {
	awsClient    *AWSSecretsManagerClient
	configClient *ConfigFileSecretsClient
	logger       *zap.Logger
}

// NewDualModeSecretsClient creates a new dual-mode secrets client
func NewDualModeSecretsClient(
	region, secretName string,
	configClientID, configClientSecret string,
	logger *zap.Logger,
) (*DualModeSecretsClient, error) {
	// Try to create AWS Secrets Manager client
	var awsClient *AWSSecretsManagerClient
	if region != "" && secretName != "" {
		var err error
		awsClient, err = NewAWSSecretsManagerClient(region, secretName, logger)
		if err != nil {
			logger.Warn("Failed to initialize AWS Secrets Manager client, will use config file fallback",
				zap.Error(err),
			)
		}
	}

	// Create config file client as fallback
	configClient := NewConfigFileSecretsClient(configClientID, configClientSecret, logger)

	return &DualModeSecretsClient{
		awsClient:    awsClient,
		configClient: configClient,
		logger:       logger,
	}, nil
}

// GetJDCredentials retrieves JD credentials, trying AWS Secrets Manager first, then config file
func (c *DualModeSecretsClient) GetJDCredentials(ctx context.Context) (*JDCredentials, error) {
	// Try AWS Secrets Manager first if available
	if c.awsClient != nil {
		c.logger.Info("Attempting to retrieve JD credentials from AWS Secrets Manager")
		credentials, err := c.awsClient.GetJDCredentials(ctx)
		if err == nil {
			c.logger.Info("Successfully retrieved JD credentials from AWS Secrets Manager")
			return credentials, nil
		}

		c.logger.Warn("Failed to retrieve JD credentials from AWS Secrets Manager, falling back to config file",
			zap.Error(err),
		)
	}

	// Fallback to config file
	return c.configClient.GetJDCredentials(ctx)
}

// GetFullConfiguration retrieves full configuration, trying AWS Secrets Manager first
func (c *DualModeSecretsClient) GetFullConfiguration(ctx context.Context) (*FullConfiguration, error) {
	// Try AWS Secrets Manager first if available
	if c.awsClient != nil {
		c.logger.Info("Attempting to retrieve full configuration from AWS Secrets Manager")
		fullConfig, err := c.awsClient.GetFullConfiguration(ctx)
		if err == nil {
			c.logger.Info("Successfully retrieved full configuration from AWS Secrets Manager")
			return fullConfig, nil
		}

		c.logger.Warn("Failed to retrieve full configuration from AWS Secrets Manager, using empty config",
			zap.Error(err),
		)
	}

	// Return empty configuration as fallback
	c.logger.Info("Using empty configuration from fallback")
	return &FullConfiguration{}, nil
}

// MockSecretsClient mock secrets client for testing
type MockSecretsClient struct {
	credentials *JDCredentials
	logger      *zap.Logger
}

// NewMockSecretsClient creates a new mock secrets client
func NewMockSecretsClient(logger *zap.Logger) *MockSecretsClient {
	return &MockSecretsClient{
		credentials: &JDCredentials{
			ClientID:     "mock_client_id",
			ClientSecret: "mock_client_secret",
		},
		logger: logger,
	}
}

// SetCredentials sets mock credentials
func (c *MockSecretsClient) SetCredentials(clientID, clientSecret string) {
	c.credentials = &JDCredentials{
		ClientID:     clientID,
		ClientSecret: clientSecret,
	}
}

// GetJDCredentials retrieves mock JD credentials
func (c *MockSecretsClient) GetJDCredentials(ctx context.Context) (*JDCredentials, error) {
	c.logger.Info("Mock: Getting JD credentials")
	return c.credentials, nil
}

// GetFullConfiguration returns empty configuration for mock client
func (c *MockSecretsClient) GetFullConfiguration(ctx context.Context) (*FullConfiguration, error) {
	c.logger.Info("Mock: Getting full configuration (empty)")
	return &FullConfiguration{}, nil
}

// maskString masks a string for logging (shows first 8 characters + ***)
func maskString(s string) string {
	if len(s) <= 8 {
		return "***"
	}
	return s[:8] + "***"
}
