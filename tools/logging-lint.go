package main

import (
	"bufio"
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// LoggingLinter 日志规范检查器
type LoggingLinter struct {
	fileSet *token.FileSet
	issues  []Issue
}

// Issue 代表一个日志规范问题
type Issue struct {
	File        string
	Line        int
	Column      int
	Type        IssueType
	Message     string
	Suggestion  string
}

// IssueType 问题类型
type IssueType string

const (
	IssueTypeBusinessField    IssueType = "BUSINESS_FIELD"
	IssueTypeMissingContext   IssueType = "MISSING_CONTEXT"
	IssueTypeSensitiveData    IssueType = "SENSITIVE_DATA"
	IssueTypeMissingTraceID   IssueType = "MISSING_TRACE_ID"
	IssueTypeOldLoggerUsage   IssueType = "OLD_LOGGER_USAGE"
)

// NewLoggingLinter 创建新的日志规范检查器
func NewLoggingLinter() *LoggingLinter {
	return &LoggingLinter{
		fileSet: token.NewFileSet(),
		issues:  make([]Issue, 0),
	}
}

// LintDirectory 检查目录中的所有Go文件
func (l *LoggingLinter) LintDirectory(dir string) error {
	return filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只检查Go文件，跳过测试文件和vendor目录
		if !strings.HasSuffix(path, ".go") ||
			strings.HasSuffix(path, "_test.go") ||
			strings.Contains(path, "vendor/") ||
			strings.Contains(path, ".git/") {
			return nil
		}

		return l.LintFile(path)
	})
}

// LintFile 检查单个文件
func (l *LoggingLinter) LintFile(filename string) error {
	// 解析Go文件
	node, err := parser.ParseFile(l.fileSet, filename, nil, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("failed to parse file %s: %w", filename, err)
	}

	// 遍历AST节点
	ast.Inspect(node, func(n ast.Node) bool {
		switch node := n.(type) {
		case *ast.CallExpr:
			l.checkLoggerCall(filename, node)
		}
		return true
	})

	return nil
}

// checkLoggerCall 检查日志调用
func (l *LoggingLinter) checkLoggerCall(filename string, call *ast.CallExpr) {
	// 检查是否是日志调用
	if !l.isLoggerCall(call) {
		return
	}

	pos := l.fileSet.Position(call.Pos())
	
	// 检查各种日志规范问题
	l.checkBusinessFieldsInZapCall(filename, pos, call)
	l.checkMissingBusinessContext(filename, pos, call)
	l.checkSensitiveDataExposure(filename, pos, call)
	l.checkOldLoggerUsage(filename, pos, call)
}

// isLoggerCall 判断是否是日志调用
func (l *LoggingLinter) isLoggerCall(call *ast.CallExpr) bool {
	if sel, ok := call.Fun.(*ast.SelectorExpr); ok {
		methodName := sel.Sel.Name
		return methodName == "Info" || methodName == "Error" || methodName == "Warn" || methodName == "Debug"
	}
	return false
}

// checkBusinessFieldsInZapCall 检查是否在zap调用中使用了业务字段
func (l *LoggingLinter) checkBusinessFieldsInZapCall(filename string, pos token.Position, call *ast.CallExpr) {
	businessFields := []string{
		"pix_key", "account", "amount", "tx_id", "document_id",
		"key_type", "currency", "from_account", "to_account",
	}

	for _, arg := range call.Args {
		if l.containsBusinessField(arg, businessFields) {
			l.addIssue(Issue{
				File:    filename,
				Line:    pos.Line,
				Column:  pos.Column,
				Type:    IssueTypeBusinessField,
				Message: "Business field found in zap call - should be merged into message",
				Suggestion: "Use logger.FormatBusinessContext() or include in message string",
			})
		}
	}
}

// containsBusinessField 检查表达式是否包含业务字段
func (l *LoggingLinter) containsBusinessField(expr ast.Expr, businessFields []string) bool {
	switch node := expr.(type) {
	case *ast.CallExpr:
		if sel, ok := node.Fun.(*ast.SelectorExpr); ok {
			if ident, ok := sel.X.(*ast.Ident); ok && ident.Name == "zap" {
				// 检查zap调用的第一个参数（字段名）
				if len(node.Args) > 0 {
					if lit, ok := node.Args[0].(*ast.BasicLit); ok {
						fieldName := strings.Trim(lit.Value, `"`)
						for _, bf := range businessFields {
							if fieldName == bf {
								return true
							}
						}
					}
				}
			}
		}
	}
	return false
}

// checkMissingBusinessContext 检查是否缺少业务上下文
func (l *LoggingLinter) checkMissingBusinessContext(filename string, pos token.Position, call *ast.CallExpr) {
	// 检查第一个参数（消息）是否包含业务上下文
	if len(call.Args) > 0 {
		if lit, ok := call.Args[0].(*ast.BasicLit); ok {
			message := strings.Trim(lit.Value, `"`)
			if l.isGenericMessage(message) {
				l.addIssue(Issue{
					File:    filename,
					Line:    pos.Line,
					Column:  pos.Column,
					Type:    IssueTypeMissingContext,
					Message: "Generic log message without business context",
					Suggestion: "Include specific business context in the message",
				})
			}
		}
	}
}

// isGenericMessage 判断是否是通用消息（缺少业务上下文）
func (l *LoggingLinter) isGenericMessage(message string) bool {
	genericPatterns := []string{
		"^Failed to parse response$",
		"^Request failed$",
		"^Operation completed$",
		"^Error occurred$",
		"^Success$",
		"^Processing request$",
	}

	for _, pattern := range genericPatterns {
		if matched, _ := regexp.MatchString(pattern, message); matched {
			return true
		}
	}
	return false
}

// checkSensitiveDataExposure 检查敏感数据暴露
func (l *LoggingLinter) checkSensitiveDataExposure(filename string, pos token.Position, call *ast.CallExpr) {
	sensitivePatterns := []string{
		`password`, `secret`, `token`, `key`, `cpf`, `cnpj`,
		`account_number`, `card_number`, `authorization`,
	}

	for _, arg := range call.Args {
		if l.containsSensitiveData(arg, sensitivePatterns) {
			l.addIssue(Issue{
				File:    filename,
				Line:    pos.Line,
				Column:  pos.Column,
				Type:    IssueTypeSensitiveData,
				Message: "Potential sensitive data exposure in log",
				Suggestion: "Use masking functions like logger.MaskSensitiveData()",
			})
		}
	}
}

// containsSensitiveData 检查是否包含敏感数据
func (l *LoggingLinter) containsSensitiveData(expr ast.Expr, sensitivePatterns []string) bool {
	// 简化实现：检查字符串字面量和标识符
	switch node := expr.(type) {
	case *ast.BasicLit:
		value := strings.ToLower(node.Value)
		for _, pattern := range sensitivePatterns {
			if strings.Contains(value, pattern) {
				return true
			}
		}
	case *ast.Ident:
		name := strings.ToLower(node.Name)
		for _, pattern := range sensitivePatterns {
			if strings.Contains(name, pattern) {
				return true
			}
		}
	}
	return false
}

// checkOldLoggerUsage 检查旧的日志使用方式
func (l *LoggingLinter) checkOldLoggerUsage(filename string, pos token.Position, call *ast.CallExpr) {
	// 检查是否直接使用zap.Logger而不是StructuredLogger
	if sel, ok := call.Fun.(*ast.SelectorExpr); ok {
		if ident, ok := sel.X.(*ast.Ident); ok {
			// 检查是否是logger.Info这种调用，但不是structuredLogger
			if ident.Name == "logger" && !l.isStructuredLoggerCall(call) {
				l.addIssue(Issue{
					File:    filename,
					Line:    pos.Line,
					Column:  pos.Column,
					Type:    IssueTypeOldLoggerUsage,
					Message: "Using old logger instead of StructuredLogger",
					Suggestion: "Use logger.NewStructuredLogger() and structured logging methods",
				})
			}
		}
	}
}

// isStructuredLoggerCall 判断是否是结构化日志调用
func (l *LoggingLinter) isStructuredLoggerCall(call *ast.CallExpr) bool {
	// 简化实现：检查是否包含fmt.Sprintf调用
	for _, arg := range call.Args {
		if callExpr, ok := arg.(*ast.CallExpr); ok {
			if sel, ok := callExpr.Fun.(*ast.SelectorExpr); ok {
				if ident, ok := sel.X.(*ast.Ident); ok {
					if ident.Name == "fmt" && sel.Sel.Name == "Sprintf" {
						return true
					}
				}
			}
		}
	}
	return false
}

// addIssue 添加问题
func (l *LoggingLinter) addIssue(issue Issue) {
	l.issues = append(l.issues, issue)
}

// PrintReport 打印检查报告
func (l *LoggingLinter) PrintReport() {
	if len(l.issues) == 0 {
		fmt.Println("✅ No logging standard violations found!")
		return
	}

	fmt.Printf("Found %d logging standard violations:\n\n", len(l.issues))

	// 按文件分组
	fileIssues := make(map[string][]Issue)
	for _, issue := range l.issues {
		fileIssues[issue.File] = append(fileIssues[issue.File], issue)
	}

	for file, issues := range fileIssues {
		fmt.Printf("📁 %s\n", file)
		for _, issue := range issues {
			fmt.Printf("  ❌ Line %d:%d [%s] %s\n", issue.Line, issue.Column, issue.Type, issue.Message)
			if issue.Suggestion != "" {
				fmt.Printf("     💡 Suggestion: %s\n", issue.Suggestion)
			}
		}
		fmt.Println()
	}

	// 统计信息
	typeCount := make(map[IssueType]int)
	for _, issue := range l.issues {
		typeCount[issue.Type]++
	}

	fmt.Println("📊 Issue Summary:")
	for issueType, count := range typeCount {
		fmt.Printf("  %s: %d\n", issueType, count)
	}
}

// GenerateFixScript 生成修复脚本
func (l *LoggingLinter) GenerateFixScript(outputFile string) error {
	file, err := os.Create(outputFile)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	writer.WriteString("#!/bin/bash\n")
	writer.WriteString("# Auto-generated logging fix script\n")
	writer.WriteString("# Please review before executing\n\n")

	for _, issue := range l.issues {
		switch issue.Type {
		case IssueTypeBusinessField:
			writer.WriteString(fmt.Sprintf("# Fix business field issue in %s:%d\n", issue.File, issue.Line))
			writer.WriteString(fmt.Sprintf("# %s\n", issue.Suggestion))
			writer.WriteString("\n")
		}
	}

	return nil
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run logging-lint.go <directory>")
		os.Exit(1)
	}

	directory := os.Args[1]
	linter := NewLoggingLinter()

	fmt.Printf("🔍 Checking logging standards in %s...\n\n", directory)

	if err := linter.LintDirectory(directory); err != nil {
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	linter.PrintReport()

	// 生成修复脚本
	if len(linter.issues) > 0 {
		if err := linter.GenerateFixScript("logging-fixes.sh"); err != nil {
			fmt.Printf("Warning: Failed to generate fix script: %v\n", err)
		} else {
			fmt.Println("📝 Generated logging-fixes.sh with suggestions")
		}
	}
}
