package config

import (
	"context"
	"fmt"
	"strings"
	"time"

	"jdpi-gateway/pkg/secretsmanager"

	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// Config application configuration structure
type Config struct {
	Server            ServerConfig         `mapstructure:"server" validate:"required"`
	JD                JDConfig             `mapstructure:"jd" validate:"required"`
	AWS               AWSConfig            `mapstructure:"aws"`
	Secrets           SecretsConfig        `mapstructure:"secrets"`
	Log               LogConfig            `mapstructure:"log" validate:"required"`
	LoadedCredentials *LoadedCredentials   `json:"-"` // Runtime loaded credentials, not from config file
	ConfigSources     *ConfigurationSource `json:"-"` // Runtime configuration sources tracking
}

// LoadedCredentials contains the actual credentials loaded from SecretManager or config file
type LoadedCredentials struct {
	ClientID     string    `json:"client_id"`
	ClientSecret string    `json:"client_secret"`
	Source       string    `json:"source"` // "secrets_manager", "config_file", or "mock"
	LoadedAt     time.Time `json:"loaded_at"`
}

// ConfigurationSource represents the source of each configuration value
type ConfigurationSource struct {
	Sources map[string]string `json:"sources"` // field_path -> source_name
}

// ServerConfig gRPC server configuration
type ServerConfig struct {
	Port int    `mapstructure:"port" validate:"required,min=1,max=65535"`
	Host string `mapstructure:"host" validate:"required"`
}

// JDConfig JD API configuration
type JDConfig struct {
	BaseURL      string `mapstructure:"base_url" validate:"required,url"`
	GrantType    string `mapstructure:"grant_type" validate:"required"`
	Scope        string `mapstructure:"scope" validate:"required"`
	ClientID     string `mapstructure:"client_id"`
	ClientSecret string `mapstructure:"client_secret"`
	CacheMode    bool   `mapstructure:"cache_mode"`
	MockMode     bool   `mapstructure:"mock_mode"`
	TimeoutSecs  int    `mapstructure:"timeout_seconds" validate:"min=1"`
}

// AWSConfig AWS configuration
type AWSConfig struct {
	Region string `mapstructure:"region"`
}

// SecretsConfig secrets configuration
type SecretsConfig struct {
	SecretName string `mapstructure:"secret_name"`
}

// LogConfig logging configuration
type LogConfig struct {
	Level            string `mapstructure:"level" validate:"required,oneof=debug info warn error"`
	Format           string `mapstructure:"format" validate:"required,oneof=json console"`
	OutputPath       string `mapstructure:"output_path"`
	EnableCaller     bool   `mapstructure:"enable_caller"`
	EnableStacktrace bool   `mapstructure:"enable_stacktrace"`
	TimeFormat       string `mapstructure:"time_format" validate:"omitempty,oneof=iso8601 rfc3339 unix"`
}

// LoadConfig loads the configuration file (basic loading without secrets)
func LoadConfig(configPath string) (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")

	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		viper.AddConfigPath("./config")
		viper.AddConfigPath(".")
	}

	// Set environment variable prefix
	viper.SetEnvPrefix("JDPI")
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Set default values
	setDefaults()

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// LoadConfigWithSecrets loads configuration with priority: SecretManager > Environment > Config File
func LoadConfigWithSecrets(configPath string, logger *zap.Logger) (*Config, secretsmanager.SecretsClient, error) {
	// Load basic configuration from file and environment
	cfg, err := LoadConfig(configPath)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to load basic config: %w", err)
	}

	logger.Info("Configuration file loaded successfully", zap.String("config_path", configPath))

	// Initialize configuration sources tracking
	cfg.ConfigSources = &ConfigurationSource{
		Sources: make(map[string]string),
	}

	// In mock mode, only use config file and environment variables
	if cfg.JD.MockMode {
		logger.Info("Running in mock mode, using config file and environment variables only")

		// Track sources for mock mode (config file + environment)
		trackConfigSources(cfg, "config_file")

		// Set up credentials from config
		cfg.LoadedCredentials = &LoadedCredentials{
			ClientID:     cfg.JD.ClientID,
			ClientSecret: cfg.JD.ClientSecret,
			Source:       "mock",
			LoadedAt:     time.Now(),
		}

		// Create mock secrets client for compatibility
		mockSecretsClient := secretsmanager.NewMockSecretsClient(logger)
		mockSecretsClient.SetCredentials(cfg.JD.ClientID, cfg.JD.ClientSecret)

		logLoadedConfig(cfg, logger)
		return cfg, mockSecretsClient, nil
	}

	// Non-mock mode: Apply SecretManager > Environment > Config File priority
	secretsClient, err := secretsmanager.NewDualModeSecretsClient(
		cfg.AWS.Region,
		cfg.Secrets.SecretName,
		cfg.JD.ClientID,
		cfg.JD.ClientSecret,
		logger,
	)
	if err != nil {
		logger.Error("Failed to initialize secrets client", zap.Error(err))
		return nil, nil, fmt.Errorf("failed to initialize secrets client: %w", err)
	}

	// Apply configuration from SecretManager with priority
	err = applySecretsManagerConfig(cfg, secretsClient, logger)
	if err != nil {
		logger.Warn("Failed to apply SecretManager configuration", zap.Error(err))
	}

	// Load credentials with priority logic
	loadedCredentials, err := loadCredentialsWithPriority(secretsClient, cfg, logger)
	if err != nil {
		logger.Error("Failed to load credentials", zap.Error(err))
		return nil, nil, fmt.Errorf("failed to load credentials: %w", err)
	}

	cfg.LoadedCredentials = loadedCredentials
	logLoadedConfig(cfg, logger)

	return cfg, secretsClient, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Default server configuration
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)

	// Default JD API configuration
	viper.SetDefault("jd.grant_type", "client_credentials")
	viper.SetDefault("jd.scope", "dict_api,qrcode_api,spi_api,auth_api")
	viper.SetDefault("jd.client_id", "")
	viper.SetDefault("jd.client_secret", "")
	viper.SetDefault("jd.cache_mode", false)
	viper.SetDefault("jd.mock_mode", false)
	viper.SetDefault("jd.timeout_seconds", 30)

	// Default AWS configuration
	viper.SetDefault("aws.region", "us-east-1")

	// Default secrets configuration
	viper.SetDefault("secrets.secret_name", "")

	// Default logging configuration
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	viper.SetDefault("log.output_path", "stdout")
}

// trackConfigSources tracks the source of configuration values
func trackConfigSources(cfg *Config, defaultSource string) {
	// Track all configuration sources as default source initially
	// This is a simplified implementation - in a real scenario, you might want to track each field individually
	cfg.ConfigSources.Sources["server.host"] = defaultSource
	cfg.ConfigSources.Sources["server.port"] = defaultSource
	cfg.ConfigSources.Sources["jd.base_url"] = defaultSource
	cfg.ConfigSources.Sources["jd.grant_type"] = defaultSource
	cfg.ConfigSources.Sources["jd.scope"] = defaultSource
	cfg.ConfigSources.Sources["jd.client_id"] = defaultSource
	cfg.ConfigSources.Sources["jd.client_secret"] = defaultSource
	cfg.ConfigSources.Sources["jd.cache_mode"] = defaultSource
	cfg.ConfigSources.Sources["jd.mock_mode"] = defaultSource
	cfg.ConfigSources.Sources["jd.timeout_seconds"] = defaultSource
	cfg.ConfigSources.Sources["aws.region"] = defaultSource
	cfg.ConfigSources.Sources["secrets.secret_name"] = defaultSource
	cfg.ConfigSources.Sources["log.level"] = defaultSource
	cfg.ConfigSources.Sources["log.format"] = defaultSource
	cfg.ConfigSources.Sources["log.output_path"] = defaultSource
	cfg.ConfigSources.Sources["log.enable_caller"] = defaultSource
	cfg.ConfigSources.Sources["log.enable_stacktrace"] = defaultSource
	cfg.ConfigSources.Sources["log.time_format"] = defaultSource
}

// applySecretsManagerConfig applies configuration from SecretManager with highest priority
func applySecretsManagerConfig(cfg *Config, secretsClient secretsmanager.SecretsClient, logger *zap.Logger) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	logger.Info("Attempting to load configuration from SecretManager...")

	fullConfig, err := secretsClient.GetFullConfiguration(ctx)
	if err != nil {
		logger.Warn("Failed to load configuration from SecretManager", zap.Error(err))
		return err
	}

	// Track initial sources as config_file/environment
	trackConfigSources(cfg, "config_file")

	// Apply SecretManager configuration with highest priority
	if fullConfig.Server != nil {
		if fullConfig.Server.Host != "" {
			cfg.Server.Host = fullConfig.Server.Host
			cfg.ConfigSources.Sources["server.host"] = "secrets_manager"
		}
		if fullConfig.Server.Port != 0 {
			cfg.Server.Port = fullConfig.Server.Port
			cfg.ConfigSources.Sources["server.port"] = "secrets_manager"
		}
	}

	if fullConfig.JD != nil {
		if fullConfig.JD.BaseURL != "" {
			cfg.JD.BaseURL = fullConfig.JD.BaseURL
			cfg.ConfigSources.Sources["jd.base_url"] = "secrets_manager"
		}
		if fullConfig.JD.GrantType != "" {
			cfg.JD.GrantType = fullConfig.JD.GrantType
			cfg.ConfigSources.Sources["jd.grant_type"] = "secrets_manager"
		}
		if fullConfig.JD.Scope != "" {
			cfg.JD.Scope = fullConfig.JD.Scope
			cfg.ConfigSources.Sources["jd.scope"] = "secrets_manager"
		}
		if fullConfig.JD.ClientID != "" {
			cfg.JD.ClientID = fullConfig.JD.ClientID
			cfg.ConfigSources.Sources["jd.client_id"] = "secrets_manager"
		}
		if fullConfig.JD.ClientSecret != "" {
			cfg.JD.ClientSecret = fullConfig.JD.ClientSecret
			cfg.ConfigSources.Sources["jd.client_secret"] = "secrets_manager"
		}
		if fullConfig.JD.CacheMode != nil {
			cfg.JD.CacheMode = *fullConfig.JD.CacheMode
			cfg.ConfigSources.Sources["jd.cache_mode"] = "secrets_manager"
		}
		if fullConfig.JD.MockMode != nil {
			cfg.JD.MockMode = *fullConfig.JD.MockMode
			cfg.ConfigSources.Sources["jd.mock_mode"] = "secrets_manager"
		}
		if fullConfig.JD.TimeoutSecs != 0 {
			cfg.JD.TimeoutSecs = fullConfig.JD.TimeoutSecs
			cfg.ConfigSources.Sources["jd.timeout_seconds"] = "secrets_manager"
		}
	}

	if fullConfig.AWS != nil {
		if fullConfig.AWS.Region != "" {
			cfg.AWS.Region = fullConfig.AWS.Region
			cfg.ConfigSources.Sources["aws.region"] = "secrets_manager"
		}
	}

	if fullConfig.Secrets != nil {
		if fullConfig.Secrets.SecretName != "" {
			cfg.Secrets.SecretName = fullConfig.Secrets.SecretName
			cfg.ConfigSources.Sources["secrets.secret_name"] = "secrets_manager"
		}
	}

	if fullConfig.Log != nil {
		if fullConfig.Log.Level != "" {
			cfg.Log.Level = fullConfig.Log.Level
			cfg.ConfigSources.Sources["log.level"] = "secrets_manager"
		}
		if fullConfig.Log.Format != "" {
			cfg.Log.Format = fullConfig.Log.Format
			cfg.ConfigSources.Sources["log.format"] = "secrets_manager"
		}
		if fullConfig.Log.OutputPath != "" {
			cfg.Log.OutputPath = fullConfig.Log.OutputPath
			cfg.ConfigSources.Sources["log.output_path"] = "secrets_manager"
		}
		if fullConfig.Log.EnableCaller != nil {
			cfg.Log.EnableCaller = *fullConfig.Log.EnableCaller
			cfg.ConfigSources.Sources["log.enable_caller"] = "secrets_manager"
		}
		if fullConfig.Log.EnableStacktrace != nil {
			cfg.Log.EnableStacktrace = *fullConfig.Log.EnableStacktrace
			cfg.ConfigSources.Sources["log.enable_stacktrace"] = "secrets_manager"
		}
		if fullConfig.Log.TimeFormat != "" {
			cfg.Log.TimeFormat = fullConfig.Log.TimeFormat
			cfg.ConfigSources.Sources["log.time_format"] = "secrets_manager"
		}
	}

	logger.Info("Successfully applied configuration from SecretManager")
	return nil
}

// loadCredentialsWithPriority loads credentials with priority: SecretManager > Environment > Config File
func loadCredentialsWithPriority(secretsClient secretsmanager.SecretsClient, cfg *Config, logger *zap.Logger) (*LoadedCredentials, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	logger.Info("Loading JD credentials...")

	// Try to get credentials from secrets manager
	credentials, err := secretsClient.GetJDCredentials(ctx)
	if err != nil {
		logger.Warn("Failed to load credentials from SecretManager, using config file fallback", zap.Error(err))

		// Fallback to config file credentials
		if cfg.JD.ClientID == "" || cfg.JD.ClientSecret == "" {
			return nil, fmt.Errorf("no valid credentials found in SecretManager or config file")
		}

		return &LoadedCredentials{
			ClientID:     cfg.JD.ClientID,
			ClientSecret: cfg.JD.ClientSecret,
			Source:       "config_file",
			LoadedAt:     time.Now(),
		}, nil
	}

	// Validate credentials from secrets manager
	if credentials.ClientID == "" || credentials.ClientSecret == "" {
		logger.Warn("Credentials from SecretManager are empty, using config file fallback")

		if cfg.JD.ClientID == "" || cfg.JD.ClientSecret == "" {
			return nil, fmt.Errorf("no valid credentials found in SecretManager or config file")
		}

		return &LoadedCredentials{
			ClientID:     cfg.JD.ClientID,
			ClientSecret: cfg.JD.ClientSecret,
			Source:       "config_file",
			LoadedAt:     time.Now(),
		}, nil
	}

	// Use credentials from secrets manager
	logger.Info("Successfully loaded credentials from SecretManager")
	return &LoadedCredentials{
		ClientID:     credentials.ClientID,
		ClientSecret: credentials.ClientSecret,
		Source:       "secrets_manager",
		LoadedAt:     time.Now(),
	}, nil
}

// logLoadedConfig logs the loaded configuration with masked sensitive information and sources
func logLoadedConfig(cfg *Config, logger *zap.Logger) {
	logger.Info("=== Configuration Summary ===")

	// Server configuration
	logger.Info("Server Configuration",
		zap.String("host", cfg.Server.Host),
		zap.String("host_source", getConfigSource(cfg, "server.host")),
		zap.Int("port", cfg.Server.Port),
		zap.String("port_source", getConfigSource(cfg, "server.port")),
	)

	// JD API configuration
	logger.Info("JD API Configuration",
		zap.String("base_url", cfg.JD.BaseURL),
		zap.String("base_url_source", getConfigSource(cfg, "jd.base_url")),
		zap.String("grant_type", cfg.JD.GrantType),
		zap.String("grant_type_source", getConfigSource(cfg, "jd.grant_type")),
		zap.String("scope", cfg.JD.Scope),
		zap.String("scope_source", getConfigSource(cfg, "jd.scope")),
		zap.Bool("cache_mode", cfg.JD.CacheMode),
		zap.String("cache_mode_source", getConfigSource(cfg, "jd.cache_mode")),
		zap.Bool("mock_mode", cfg.JD.MockMode),
		zap.String("mock_mode_source", getConfigSource(cfg, "jd.mock_mode")),
		zap.Int("timeout_seconds", cfg.JD.TimeoutSecs),
		zap.String("timeout_seconds_source", getConfigSource(cfg, "jd.timeout_seconds")),
	)

	// AWS configuration
	logger.Info("AWS Configuration",
		zap.String("region", cfg.AWS.Region),
		zap.String("region_source", getConfigSource(cfg, "aws.region")),
	)

	// Secrets configuration
	logger.Info("Secrets Configuration",
		zap.String("secret_name", cfg.Secrets.SecretName),
		zap.String("secret_name_source", getConfigSource(cfg, "secrets.secret_name")),
	)

	// Loaded credentials (masked)
	if cfg.LoadedCredentials != nil {
		logger.Info("Loaded Credentials",
			zap.String("client_id", maskString(cfg.LoadedCredentials.ClientID)),
			zap.String("client_id_source", getConfigSource(cfg, "jd.client_id")),
			zap.String("client_secret", maskString(cfg.LoadedCredentials.ClientSecret)),
			zap.String("client_secret_source", getConfigSource(cfg, "jd.client_secret")),
			zap.String("credentials_source", cfg.LoadedCredentials.Source),
			zap.Time("loaded_at", cfg.LoadedCredentials.LoadedAt),
		)
	}

	// Log configuration
	logger.Info("Log Configuration",
		zap.String("level", cfg.Log.Level),
		zap.String("level_source", getConfigSource(cfg, "log.level")),
		zap.String("format", cfg.Log.Format),
		zap.String("format_source", getConfigSource(cfg, "log.format")),
		zap.String("output_path", cfg.Log.OutputPath),
		zap.String("output_path_source", getConfigSource(cfg, "log.output_path")),
		zap.Bool("enable_caller", cfg.Log.EnableCaller),
		zap.String("enable_caller_source", getConfigSource(cfg, "log.enable_caller")),
		zap.Bool("enable_stacktrace", cfg.Log.EnableStacktrace),
		zap.String("enable_stacktrace_source", getConfigSource(cfg, "log.enable_stacktrace")),
		zap.String("time_format", cfg.Log.TimeFormat),
		zap.String("time_format_source", getConfigSource(cfg, "log.time_format")),
	)

	logger.Info("=== Configuration Summary End ===")
}

// getConfigSource returns the source of a configuration value
func getConfigSource(cfg *Config, key string) string {
	if cfg.ConfigSources != nil {
		if source, exists := cfg.ConfigSources.Sources[key]; exists {
			return source
		}
	}
	return "unknown"
}

// maskString masks a string for logging (shows first 8 characters + ***)
func maskString(s string) string {
	if len(s) <= 8 {
		return "***"
	}
	return s[:8] + "***"
}

// GetEffectiveCredentials returns the effective credentials to use
func (c *Config) GetEffectiveCredentials() (clientID, clientSecret string) {
	if c.LoadedCredentials != nil {
		return c.LoadedCredentials.ClientID, c.LoadedCredentials.ClientSecret
	}
	return c.JD.ClientID, c.JD.ClientSecret
}
