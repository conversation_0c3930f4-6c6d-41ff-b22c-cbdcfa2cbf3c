server:
  host: "0.0.0.0"
  port: 8080

jd:
  base_url: "http://proxy.hml.a55.internal:8080"
  grant_type: "client_credentials"
  scope: "dict_api,qrcode_api,spi_api,auth_api"
  client_id: "admapi"
  client_secret: "$xRjn?--Fh1_etep0J7_0ucC$G$R04-E"
  mock_mode: false
  timeout_seconds: 30

aws:
  region: "us-west-2"

secrets:
  secret_name: "jdpi/client_id"

log:
  level: "debug"
  format: "json"
  output_path: "stdout"
