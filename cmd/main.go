package main

import (
	"context"
	"flag"
	"fmt"
	"go.uber.org/zap/zapcore"
	"jdpi-gateway/config"
	"jdpi-gateway/internal/client"
	"jdpi-gateway/internal/handler"
	"jdpi-gateway/internal/mock"
	"jdpi-gateway/internal/server"
	"jdpi-gateway/internal/service"
	"log"
	"os"
	"time"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

var (
	configPath = flag.String("config", "./config/config.yaml", "Path to configuration file")
	version    = flag.Bool("version", false, "Show version information")
)

const (
	AppName    = "jdpi-gateway"
	AppVersion = "1.1.1"
)

func main() {
	// Step 1: Parse command line arguments
	parseCommandLineArgs()

	// Step 2: Load and validate configuration
	cfg, logger := loadAndValidateConfig()
	defer logger.Sync()

	// Step 3: Initialize JDPI Client
	pixDictClient, pixQRCodeClient, pixPaymentClient, authService := initializeJDPIClient(cfg, logger)

	// Step 4: Initialize Service and Handler layers
	authHandler, pixDictHandler, pixQRCodeHandler, pixPaymentHandler := initializeServiceAndHandler(pixDictClient, pixQRCodeClient, pixPaymentClient, authService, cfg, logger)

	// Step 5: Initialize and start gRPC server
	startGRPCServer(cfg, logger, authHandler, pixDictHandler, pixQRCodeHandler, pixPaymentHandler)
}

// parseCommandLineArgs parses command line arguments and handles version display
func parseCommandLineArgs() {
	flag.Parse()

	// Show version information
	if *version {
		fmt.Printf("%s version %s\n", AppName, AppVersion)
		os.Exit(0)
	}
}

// initBasicLogger initializes a basic logger for configuration loading
func initBasicLogger() (*zap.Logger, error) {
	// Create a simple development logger for configuration loading
	zapConfig := zap.NewDevelopmentConfig()
	zapConfig.Encoding = "json"
	zapConfig.Level = zap.NewAtomicLevelAt(zap.InfoLevel)

	logger, err := zapConfig.Build()
	if err != nil {
		return nil, fmt.Errorf("failed to build basic logger: %w", err)
	}

	return logger, nil
}

// initLogger initializes the logger with full configuration
func initLogger(cfg config.LogConfig) (*zap.Logger, error) {
	// Parse log level
	level, err := zapcore.ParseLevel(cfg.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}

	// Create logger configuration
	var zapConfig zap.Config
	if cfg.Format == "json" {
		zapConfig = zap.NewProductionConfig()
	} else {
		zapConfig = zap.NewDevelopmentConfig()
	}

	zapConfig.Level = zap.NewAtomicLevelAt(level)

	// Set output path
	if cfg.OutputPath != "" && cfg.OutputPath != "stdout" {
		zapConfig.OutputPaths = []string{cfg.OutputPath}
	}

	// Configure time format
	if cfg.TimeFormat != "" {
		switch cfg.TimeFormat {
		case "iso8601":
			zapConfig.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
		case "rfc3339":
			zapConfig.EncoderConfig.EncodeTime = zapcore.RFC3339TimeEncoder
		case "unix":
			zapConfig.EncoderConfig.EncodeTime = func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
				enc.AppendInt64(t.Unix())
			}
		}
	}

	// Build logger with options
	options := []zap.Option{}

	// Add caller information if enabled
	if cfg.EnableCaller {
		options = append(options, zap.AddCaller())
		options = append(options, zap.AddCallerSkip(1))
	}

	// Add stack trace if enabled
	if cfg.EnableStacktrace {
		options = append(options, zap.AddStacktrace(zapcore.ErrorLevel))
	} else {
		// Default: only add stack trace for errors
		options = append(options, zap.AddStacktrace(zapcore.ErrorLevel))
	}

	// Build logger
	logger, err := zapConfig.Build(options...)
	if err != nil {
		return nil, fmt.Errorf("failed to build logger: %w", err)
	}

	return logger, nil
}

// loadAndValidateConfig loads configuration with secrets integration and validates it
func loadAndValidateConfig() (*config.Config, *zap.Logger) {
	// Initialize basic logger for configuration loading
	basicLogger, err := initBasicLogger()
	if err != nil {
		log.Fatalf("Failed to initialize basic logger: %v", err)
	}

	// Load configuration with secret integration
	cfg, _, err := config.LoadConfigWithSecrets(*configPath, basicLogger)
	if err != nil {
		basicLogger.Fatal("Failed to load config with secrets", zap.Error(err))
	}

	// Validate configuration
	validator := validator.New()
	if err := validator.Struct(cfg); err != nil {
		basicLogger.Fatal("Invalid configuration", zap.Error(err))
	}

	// Initialize final logger with loaded configuration
	logger, err := initLogger(cfg.Log)
	if err != nil {
		basicLogger.Fatal("Failed to initialize logger", zap.Error(err))
	}

	logger.Info("Starting JDPI Gateway",
		zap.String("app_name", AppName),
		zap.String("version", AppVersion),
	)

	return cfg, logger
}

// initializeJDPIClient initializes the JDPI client based on configuration
func initializeJDPIClient(cfg *config.Config, logger *zap.Logger) (client.PixDictClient, client.PixQRCodeClient, client.PixPaymentClient, service.AuthService) {
	logger.Info("Initializing JDPI client components")

	if cfg.JD.MockMode {
		logger.Info("Initializing clients in MOCK mode")
		// Use mock clients directly
		mockPixDictClient := mock.NewMockPixDictClient(logger)
		mockPixQRCodeClient := mock.NewMockPixQRCodeClient(logger)
		mockPixPaymentClient := mock.NewMockPixPaymentClient(logger)
		mockAuthClient := mock.NewMockAuthClient(logger)
		mockAuthService := service.NewJDAuthService(mockAuthClient, logger)
		return mockPixDictClient, mockPixQRCodeClient, mockPixPaymentClient, mockAuthService
	} else {
		logger.Info("Initializing HTTP clients")
		// Initialize base HTTP client
		baseClient := client.NewHTTPJDPIClient(cfg, logger)

		// Initialize real clients
		authClient := client.NewJDAuthClient(baseClient, cfg, logger)
		authService := service.NewJDAuthService(authClient, logger)
		pixDictClient := client.NewJDPixDictClient(baseClient, authService, logger)
		pixQRCodeClient := client.NewJDPixQRCodeClient(baseClient, authService, logger)
		pixPaymentClient := client.NewJDPixPaymentClient(baseClient, authService, logger)

		// Start token refresh routine
		go authService.StartTokenRefreshRoutine(context.Background())

		return pixDictClient, pixQRCodeClient, pixPaymentClient, authService
	}
}

// initializeServiceAndHandler initializes service and handler layers
func initializeServiceAndHandler(pixDictClient client.PixDictClient, pixQRCodeClient client.PixQRCodeClient, pixPaymentClient client.PixPaymentClient, authService service.AuthService, cfg *config.Config, logger *zap.Logger) (*handler.AuthHandler, *handler.PixDictHandler, *handler.PixQRCodeHandler, *handler.PixPaymentHandler) {
	logger.Info("Initializing service and handler layers")

	// Initialize PIX services
	pixDictService := service.NewPixDictService(pixDictClient, authService, logger)
	pixQRCodeService := service.NewJDPixQRCodeService(pixQRCodeClient, authService, logger)
	pixPaymentService := service.NewJDPixPaymentService(pixPaymentClient, authService, logger)

	// Initialize handler layer
	authHandler := handler.NewAuthHandler(authService, logger)
	pixDictHandler := handler.NewPixDictHandler(pixDictService, logger)
	pixQRCodeHandler := handler.NewPixQRCodeHandler(pixQRCodeService, logger)
	pixPaymentHandler := handler.NewPixPaymentHandler(pixPaymentService, logger)

	return authHandler, pixDictHandler, pixQRCodeHandler, pixPaymentHandler
}

// startGRPCServer initializes and starts the gRPC server
func startGRPCServer(cfg *config.Config, logger *zap.Logger, authHandler *handler.AuthHandler, pixDictHandler *handler.PixDictHandler, pixQRCodeHandler *handler.PixQRCodeHandler, pixPaymentHandler *handler.PixPaymentHandler) {
	logger.Info("Initializing gRPC server")

	// Initialize server
	srv := server.NewServer(cfg, logger, authHandler, pixDictHandler, pixQRCodeHandler, pixPaymentHandler)

	// Start server
	logger.Info("JDPI Gateway initialized successfully, starting server...")
	if err := srv.Start(AppVersion); err != nil {
		logger.Fatal("Failed to start server", zap.Error(err))
	}
}
