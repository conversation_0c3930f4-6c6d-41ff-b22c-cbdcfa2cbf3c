syntax = "proto3";

import "services/pix/response/commonResponse.proto";
import "wallet/pixTransaction.proto";
import "google/protobuf/timestamp.proto";

package response;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response";

message WebhookPixInValidationResponse {
    oneof result {
        WebhookPixInValidationResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message WebhookPixInValidationResponseSuccess {
    bool is_valid = 1;
    wallet.PixInValidationReasonCode code = 2;
    string additional_refund_info = 3;
    google.protobuf.Timestamp validation_time = 4;
}

/**
 *
 */
message WebhookPixInConfirmResponse {
    oneof result {
        WebhookPixInConfirmResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message WebhookPixInConfirmResponseSuccess {
    string request_id = 1;
    string transaction_id = 2;
    google.protobuf.Timestamp confirm_time = 4;
}

/**
 *
 */
message WebhookPixInRefundConfirmResponse {
    oneof result {
        WebhookPixInRefundConfirmResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message WebhookPixInRefundConfirmResponseSuccess {
    string request_id = 1;
    string transaction_id = 2;
    google.protobuf.Timestamp operation_time = 4;
}