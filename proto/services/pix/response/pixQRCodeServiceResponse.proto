syntax = "proto3";

import "services/pix/response/commonResponse.proto";
import "wallet/pixQRCode.proto";

package response;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response";

message QRCodeStaticCreateResponse {
    oneof result {
        QRCodeStaticCreateResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message QRCodeStaticCreateResponseSuccess {
    string qrcode_image_base64 = 1;
    string qrcode_payload_base64 = 2;
}

message QRCodeDynamicCreateResponse {
    oneof result {
        QRCodeDynamicCreateResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message QRCodeDynamicCreateResponseSuccess {
    string qrcode_image_base64 = 1;
    string qrcode_payload_base64 = 2;
    string payload_jws = 3;
    string document_id = 4;
}

message QRCodeDynamicDecodeByUrlResponse {
    oneof result {
        QRCodeDynamicDecodeByUrlResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message QRCodeDynamicDecodeByUrlResponseSuccess {
    string end_to_end_id = 1;
    wallet.QRCodeType qrcode_type = 2;
    wallet.DynamicQRCodeData dynamic_qrcode_data = 3;
}

message QRCodeGetByPayloadResponse {
    oneof result {
        QRCodeGetByPayloadResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message QRCodeGetByPayloadResponseSuccess {
    string end_to_end_id = 1;
    wallet.QRCodeType qrcode_type = 2;
    wallet.StaticQRCodeData static_qrcode_data = 3;
    wallet.DynamicQRCodeData dynamic_qrcode_data = 4;
}