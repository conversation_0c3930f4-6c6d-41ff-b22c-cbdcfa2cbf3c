syntax = "proto3";

import "wallet/pixKey.proto";
import "services/pix/response/commonResponse.proto";

package response;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response";


/**
 *
 */
message PixKeyPolicyResponse {
    oneof result {
        PixKeyPolicyResponseSuccess response = 1;
        request.Error error = 2;
    }

}

message PixKeyPolicyResponseSuccess {
    string bucket_category = 2;
    wallet.PixKeyPolicy policy = 3;
}

/**
 *
 */
message PixKeyPolicyStatusResponse {
    oneof result {
        PixKeyPolicyStatusResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixKeyPolicyStatusResponseSuccess {
    string bucket_category = 2;
    wallet.PixKeyPolicy policy = 3;
}

/**
 *
 */
message PixParticipantBalanceGetResponse {
    oneof result {
        PixParticipantBalanceGetResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixParticipantBalanceGetResponseSuccess {
    string bucket_category = 2;
    wallet.PixKeyPolicy policy = 3;
}