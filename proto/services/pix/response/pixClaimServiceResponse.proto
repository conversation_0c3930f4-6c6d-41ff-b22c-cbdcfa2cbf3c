syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "services/pix/response/commonResponse.proto";
import "wallet/pixKey.proto";

package response; // Nome do pacote Protobuf para os serviços

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response";

/**
 *
 */
message PixClaimCreateResponse {
    oneof result {
        PixClaimCreateResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixClaimCreateResponseSuccess {
    int32 ispb_donator = 2;
    string claim_id = 3;
    wallet.PixKeyClaimStatus claim_status = 4;

    google.protobuf.Timestamp claim_resolution_limit = 5; // Claim Resolution Limit Date and Time for donator
    google.protobuf.Timestamp claim_conclusion_limit = 6; // Claim Limit for requester to confirm the claim
    google.protobuf.Timestamp claim_last_modified_at = 7;
}

/**
 *
 */
message PixClaimCancelResponse {
    oneof result {
        PixClaimCancelResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message PixClaimCancelResponseSuccess {
}

/**
 *
 */
message PixClaimConfirmResponse {
    oneof result {
        PixClaimConfirmResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message PixClaimConfirmResponseSuccess {
}

/**
 *
 */
message PixClaimListByAccountResponse {
    oneof result {
        PixClaimListByAccountResponseSuccess response = 1;
        request.Error error = 2;
    }
}
message PixClaimListByAccountResponseSuccess {
}