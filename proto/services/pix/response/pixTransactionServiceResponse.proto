syntax = "proto3";

import "services/pix/response/commonResponse.proto";
import "google/protobuf/timestamp.proto";
import "wallet/pixTransaction.proto";
import "wallet/banking.proto";

package response;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response";

/**
 * PixOutConfirm Response
 */
message PixOutConfirmResponse {
    oneof result {
        PixOutConfirmResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixOutConfirmResponseSuccess {
    string request_id = 1;
    string channel_request_id = 2;
    string end_to_end_id = 3;
    google.protobuf.Timestamp channel_request_time = 4;
    wallet.PixTransactionChannelType channel = 5;
}

/**
 * PixTransactionGetByEndToEndId Response
 */
message PixTransactionGetResponse {
    oneof result {
        PixTransactionGetResponseSuccess response = 1;
        request.Error error = 2;
    }
}

message PixTransactionGetResponseSuccess {
    // Request identification
    string channel_request_id = 1;
    google.protobuf.Timestamp channel_request_time = 2;

    // Transaction Status
    wallet.PixTransactionStatus transaction_status = 3;
    wallet.PixTransactionIntermediateStatus intermediate_status = 4;
    google.protobuf.Timestamp status_updated_time = 5;
    string error_code = 6;
    string error_description = 7;
    google.protobuf.Timestamp transaction_effective_time = 8;

    // Transaction details
    wallet.PixTransactionType transaction_type = 9;
    wallet.PixTransactionPriority liquidation_priority = 10;
    wallet.PixTransactionPriorityType payment_priority = 11;
    wallet.PixTransactionPurpose purpose = 12;
    wallet.PixWithdrawalAgentType agent_modality = 13;

    // Payer information
    wallet.BankAccount payer = 14;
    wallet.BankAccountHolder payer_holder = 15;

    // Payee information
    wallet.BankAccount payee = 16;
    wallet.BankAccountHolder payee_holder = 17;

    // Payment details
    int64 initiator_cnpj = 18;
    double amount = 19;
    string end_to_end_id = 20;
    repeated wallet.TransactionAmountDetail amount_details = 21;
    string pix_key = 22;
    string payee_reconciliation_id = 23;
    string client_info = 24;
}
