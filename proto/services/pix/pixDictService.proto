syntax = "proto3";

import "services/pix/request/pixDictServiceRequest.proto";
import "services/pix/response/pixDictServiceResponse.proto";

package services;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix";

service PixDictService {
    rpc PixKeyCreate (request.PixKeyCreateRequest) returns (response.PixKeyCreateResponse);
    rpc PixKeyDelete (request.PixKeyDeleteRequest) returns (response.PixKeyDeleteResponse);
    rpc PixKeyUpdate (request.PixKeyUpdateRequest) returns (response.PixKeyUpdateResponse);

    // list pix keys linked to a specific bank accounts
    rpc PixKeyListByAccount (request.PixKeyListByAccountRequest) returns (response.PixKeyListByAccountResponse);

    // check if pix key exists
    rpc PixKeyIsExist (request.PixKeyIsExistRequest) returns (response.PixKeyIsExistResponse);

    // Used as first step when initiating a payment.
    rpc PixKeyGet (request.PixKeyGetRequest) returns (response.PixKeyGetResponse);

    // By notifying Account Closure, all the pix keys linked to this account, it will be deleted
    rpc NotifyAccountClosure (request.NotifyAccountClosureRequest) returns (response.NotifyAccountClosureResponse);
}