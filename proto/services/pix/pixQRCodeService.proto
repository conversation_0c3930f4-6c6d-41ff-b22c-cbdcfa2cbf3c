syntax = "proto3";

import "services/pix/request/pixQRCodeServiceRequest.proto";
import "services/pix/response/pixQRCodeServiceResponse.proto";

package services;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix";

service PixQRCodeService {
    rpc QRCodeStaticCreate (request.QRCodeStaticCreateRequest) returns (response.QRCodeStaticCreateResponse);

    rpc QRCodeDynamicCreate (request.QRCodeDynamicCreateRequest) returns (response.QRCodeDynamicCreateResponse);
    rpc QRCodeDynamicDecodeByUrl (request.QRCodeDynamicDecodeByUrlRequest) returns (response.QRCodeDynamicDecodeByUrlResponse);

    rpc QRCodeGetByPayload (request.QRCodeGetByPayloadRequest) returns (response.QRCodeGetByPayloadResponse);
}