syntax = "proto3";

package request;

import "google/protobuf/timestamp.proto";
import "services/pix/request/commonRequest.proto";
import "wallet/banking.proto";
import "wallet/pixTransaction.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request";

message PixOutConfirmRequest {
    request.Header header = 1;

    // Request identification
    string request_id = 2;
    google.protobuf.Timestamp request_time = 3;

    // Transaction details
    wallet.PixTransactionType transaction_type = 4;
    wallet.PixTransactionPriority liquidation_priority = 5;
    wallet.PixTransactionPriorityType payment_priority = 6;
    wallet.PixTransactionPurpose purpose = 7;
    wallet.PixWithdrawalAgentType agent_modality = 8;

    // PSS information
    int32 pss_ispb = 9;

    // Payer information
    wallet.BankAccount payer = 10;
    wallet.BankAccountHolder payer_holder = 11;

    // Payee information
    wallet.BankAccount payee = 12;
    wallet.BankAccountHolder payee_holder = 13;

    // Payment details
    double amount = 14;
    repeated wallet.TransactionAmountDetail amount_details = 15;    // Only required when purpose != PixTransactionPurpose.PURCHASE_OR_TRANSFER
    string pix_key = 16;
    string end_to_end_id = 17;
    string payee_reconciliation_id = 18;                            // TxID, if exists
    string client_info = 19;                                        // Info to be exchanged between clients (NOTES)
}

message PixTransactionGetRequest {
    request.Header header = 1;

    string channel_request_id = 2;                                  // Transaction Identifier. E.g: Delbank uses End2EndId, JD uses a gid
}