syntax = "proto3";

package request;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request";

message Header {
    // !!Pass forward only if necessary!! it might increase billing on downstream
    string idempotence_id = 1;              // used for caching, 24h in JD
    ChannelAdapter channel = 4;             // Used to route the request through channels
}

enum ChannelAdapter {
    Delbank = 0;
    A55_JD = 1;
}