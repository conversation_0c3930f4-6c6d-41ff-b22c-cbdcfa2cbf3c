syntax = "proto3";

package request;

import "wallet/pixKey.proto";
import "services/pix/request/commonRequest.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request";

message PixKeyPolicyRequest {
    request.Header header = 1;
}

message PixKeyPolicyStatusRequest {
    request.Header header = 1;
    wallet.PixKeyBucketPolicy policy = 2;
}

message PixParticipantBalanceGetRequest {
    request.Header header = 1;
}