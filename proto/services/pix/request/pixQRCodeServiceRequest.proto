syntax = "proto3";

package request;

import "wallet/banking.proto";
import "wallet/pixKey.proto";
import "wallet/pixQRCode.proto";
import "services/pix/request/commonRequest.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request";

message QRCodeStaticCreateRequest {
    request.Header header = 1;

    string txId = 2;
    wallet.QRCodeResponseType format = 3;
    wallet.PixKey pix_key = 4;
    double amount = 5;
    string pix_key_owner_name = 6;
    string pix_key_city = 7;
}

message QRCodeDynamicCreateRequest {
    request.Header header = 1;

    string txId = 2;                        // alphanumeric code that serves as a unique identifier for a Pix charge or payment request

    wallet.QRCodeResponseType format = 3;
    wallet.PixKey pix_key = 4;
    string pix_key_owner_name = 5;
    string pix_key_city = 6;

    // not required, but if set, the payer must match with this info.
    wallet.BankAccountHolder payer = 7;

    // number of seconds to define when qrcode will be expired, default = 86400.
    int32 expire_in = 8;

    // defines if QRCode can be reused, default is no.
    bool reusable = 9;
}


message QRCodeDynamicDecodeByUrlRequest {
    request.Header header = 1;

    string end_to_end_id = 2;
    string payer_document_id = 3;

    string qrcode_payload_url = 4;
}

message QRCodeGetByPayloadRequest {
    request.Header header = 1;

    string end_to_end_id = 2;
    string payer_document_id = 3;

    string qrcode_payload = 4;
}