syntax = "proto3";

package request;

import "google/protobuf/timestamp.proto";
import "services/pix/request/commonRequest.proto";
import "wallet/banking.proto";
import "wallet/pixKey.proto";
import "wallet/pixTransaction.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request";

message WebhookPixInValidationRequest {
    request.Header header = 1;

//    string request_id = 2; // NO NEED

    wallet.PixTransactionType transaction_type = 3;
    wallet.PixTransactionPurpose transaction_purpose = 4;
    wallet.PixTransactionPriority transaction_priority = 5;
    wallet.PixTransactionPriorityType transaction_priority_type = 6;
    google.protobuf.Timestamp transaction_time = 7;
    bool transaction_is_refund = 8;

    double transaction_amount = 9;
    string transaction_additional_info = 10; // Notes to be exchanged between End Users

    string ispb_document_id = 11;

    wallet.BankAccount payer_bank_account = 12;
    wallet.BankAccountHolder payer_bank_account_holder = 13;
    wallet.BankAccount payee_bank_account = 14;
    wallet.BankAccountHolder payee_bank_account_holder = 15;

    wallet.TransactionDetails transaction_details = 16; // used whenever it is a pixIn transaction
    wallet.TransactionRefundDetails transaction_refund_details = 17; // used whenever it is a refund transaction

}

message WebhookPixInConfirmRequest {
    request.Header header = 1;
    string request_id = 2;

    string end_to_end_id = 3;
    wallet.PixTransactionType transaction_type = 4;
    wallet.PixTransactionPurpose transaction_purpose = 5;
    wallet.PixTransactionPriority transaction_priority = 6;
    wallet.PixTransactionPriorityType transaction_priority_type = 7;
    google.protobuf.Timestamp transaction_time = 8;
    string transaction_additional_info = 9; // Notes to be exchanged between End Users

    string ispb_document_id = 10;
    wallet.PixKey pix_key = 11;

    wallet.BankAccount payer_bank_account = 12;
    wallet.BankAccountHolder payer_bank_account_holder = 13;
    wallet.BankAccount payee_bank_account = 14;
    wallet.BankAccountHolder payee_bank_account_holder = 15;
}

message WebhookPixInRefundConfirmRequest {
    request.Header header = 1;
    string request_id = 2;

    wallet.TransactionRefundDetails refund_details = 3;
    google.protobuf.Timestamp transaction_time = 4;
    double transaction_amount = 5;
    string transaction_additional_info = 6; // Notes to be exchanged between End Users

    wallet.BankAccount payer_bank_account = 7;
    wallet.BankAccountHolder payer_bank_account_holder = 8;

}