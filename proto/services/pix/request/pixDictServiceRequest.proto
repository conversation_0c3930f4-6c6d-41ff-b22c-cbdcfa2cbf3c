syntax = "proto3";

package request;

import "wallet/banking.proto";
import "wallet/pixKey.proto";
import "services/pix/request/commonRequest.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request";

message PixKeyCreateRequest {
    request.Header header = 1;

    int32 ispb = 2;                                         // 8 Digits code, "ID number" for financial institutions within Brazil's payment infrastructure.
    wallet.PixKey pix_key = 3;
    wallet.BankAccount bank_account = 4;
    wallet.BankAccountHolder bank_account_holder = 5;
    wallet.PixKeyCreateReason reason = 6;
}

message PixKeyDeleteRequest {
    request.Header header = 1;

    int32 ispb = 2;                             // 8 Digits code, "ID number" for financial institutions within Brazil's payment infrastructure.
    wallet.PixKey pix_key = 3;
    wallet.PixKeyDeleteReason reason = 4;
}


message PixKeyUpdateRequest {
    request.Header header = 1;

    int32 ispb = 2;                                         // 8 Digits code, "ID number" for financial institutions within Brazil's payment infrastructure.
    wallet.PixKey pix_key = 3;
    wallet.BankAccount bank_account = 5;
    wallet.BankAccountHolder bank_account_holder = 6;
    wallet.PixKeyUpdateReason reason = 7;
}

message PixKeyListByAccountRequest {
    request.Header header = 1;

    int32 ispb = 2;                                     // 8 Digits code, "ID number" for financial institutions within Brazil's payment infrastructure.
    wallet.BankAccount bank_account = 3;
    wallet.BankAccountHolder bank_account_holder = 4;
}

message PixKeyIsExistRequest {
    request.Header header = 1;

    int32 ispb = 2;                     // 8 Digits code, "ID number" for financial institutions within Brazil's payment infrastructure.
    repeated wallet.PixKey keys = 3;
}

message PixKeyGetRequest {
    request.Header header = 1;

    int32 ispb = 2;                     // 8 Digits code, "ID number" for financial institutions within Brazil's payment infrastructure.
    string e2e_id = 3;
    string requester_document_id = 4;

    wallet.PixKey pix_key = 5;
}

message NotifyAccountClosureRequest {
    request.Header header = 1;

    int32 ispb = 2;                         // 8 Digits code, "ID number" for financial institutions within Brazil's payment infrastructure.
    wallet.BankAccount bank_account = 3;
}