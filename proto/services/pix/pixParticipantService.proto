syntax = "proto3";

import "services/pix/request/pixParticipantServiceRequest.proto";
import "services/pix/response/pixParticipantServiceResponse.proto";

package services;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix";

service PixParticipantManagementService {
    rpc PixParticipantPolicyGet (request.PixKeyPolicyRequest) returns (response.PixKeyPolicyResponse);
    rpc PixParticipantPolicyStatusGet (request.PixKeyPolicyStatusRequest) returns (response.PixKeyPolicyStatusResponse);

    rpc PixParticipantBalanceGet (request.PixParticipantBalanceGetRequest) returns (response.PixParticipantBalanceGetResponse);
}