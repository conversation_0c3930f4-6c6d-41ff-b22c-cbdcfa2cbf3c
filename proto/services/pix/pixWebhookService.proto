syntax = "proto3";

package services;

import "services/pix/request/pixWebhookServiceRequest.proto";
import "services/pix/response/pixWebhookServiceResponse.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix";

service PixWebhookService {
    rpc PixInValidation (request.WebhookPixInValidationRequest) returns (response.WebhookPixInValidationResponse);
    rpc PixInConfirm (request.WebhookPixInConfirmRequest) returns (response.WebhookPixInConfirmResponse);
    rpc PixRefundConfirm (request.WebhookPixInRefundConfirmRequest) returns (response.WebhookPixInRefundConfirmResponse);
}