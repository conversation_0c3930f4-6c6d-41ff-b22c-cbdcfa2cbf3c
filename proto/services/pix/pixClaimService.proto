syntax = "proto3";

import "services/pix/request/pixClaimServiceRequest.proto";
import "services/pix/response/pixClaimServiceResponse.proto";

package services;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix";

service PixDictService {

    rpc PixKeyClaimCreate (request.PixClaimCreateRequest) returns (response.PixClaimCreateResponse);
    rpc PixKeyClaimCancel (request.PixClaimCancelRequest) returns (response.PixClaimCancelResponse);
    rpc PixKeyClaimConfirm (request.PixClaimConfirmRequest) returns (response.PixClaimConfirmResponse);
    rpc PixKeyClaimListByAccount (request.PixClaimListByAccountRequest) returns (response.PixClaimListByAccountResponse);
}