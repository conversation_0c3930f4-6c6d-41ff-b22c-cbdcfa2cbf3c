syntax = "proto3";

package jdpi;

option go_package = "jdpi-gateway/proto;jdpi";

import "proto/base.proto";

// JDPI Gateway Service
service AuthService {
  // Get JD access token
  rpc GetAccessToken(GetAccessTokenRequest) returns (GetAccessTokenResponse);
}

// Get access token request
message GetAccessTokenRequest {
  // Request ID (for tracing)
  string request_id = 1;
}

// Get access token response
message GetAccessTokenResponse {
  // Response status
  ResponseStatus status = 1;
  // Access token data
  AccessTokenData data = 2;
}

// Access token data
message AccessTokenData {
  // Access token
  string access_token = 1;
  // Token type
  string token_type = 2;
  // Expiration time (seconds)
  int64 expires_in = 3;
  // Scope
  string scope = 4;
  // Token expiration timestamp (UTC)
  int64 expires_at = 5;
}
