syntax = "proto3";

package wallet;

import "wallet/pixKey.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet";

enum PixTransactionType {
    MANUAL = 0;                     // Equivalent to Bank Transfer
    PIX_KEY = 1;                    // Transfer by PIX KEY
    STATIC_QRCODE = 2;              // Transfer Static QrCode
    DYNAMIC_QRCODE = 3;             // Payment by Dynamic QrCode
    PAYMENT_INITIATION_SERVICE = 6;
    PAYER_QRCODE = 7;
    AUTOMATIC_PIX = 8;              // For Automatic Payments
    PIX_APPROXIMATION = 9;          // Pix by Approximation
}

enum PixTransactionPurpose {
    PURCHASE_OR_TRANSFER = 0;
    PIX_CHANGE = 1;
    PIX_WITHDRAWAL = 2;
}

enum PixTransactionPriority {
    PRIORITY_LIQUIDATION = 0; // the transaction should be processed right away.
    NON_PRIORITY_LIQUIDATION = 1; // transaction can wait to be processed, like in Scheduled PIX.
}

enum PixTransactionPriorityType {
    PRIORITY_PAYMENT = 0;
    PAYMENT_UNDER_FRAUD_ANALYSIS = 1;
    SCHEDULED_PAYMENT = 2;
}

enum PixWithdrawalAgentType {
    WITHDRAWAL_FACILITATOR = 0;
    COMMERCIAL_ESTABLISHMENT = 1;
    OTHER_LEGAL_ENTITY = 2;
}

enum PixAmountType {
    CASH_AVAILABLE = 0; // Money coming from Cash
    PURCHASE_VALUE = 1; // Money coming from a Purchase
}

enum PixTransactionStatus {
    TRANSACTION_WAITING = 0;
    TRANSACTION_SUCCESS = 9;
    TRANSACTION_ERROR = 10; // Changed from -1 to 10
}

enum PixTransactionIntermediateStatus {
    SUCCESS = 0;
    PROCESSING_ERROR = 1;
    VALIDATION_ERROR = 2;
    SPI_ERROR = 3;
    NO_SPI_RESPONSE = 5;
    SENT_TO_SPI = 8;
    ACCEPTED_WAITING = 9;
}

enum TransactionRefundType {
    PSP_INTERNAL_ISSUE = 0;        // PSP Payer had an internal issue
    FRAUD_DETECTION = 1;           // PSP identifies transaction as fraud
    PAYEE_REQUEST = 2;             // Payee's request
    PAYEE_REQUEST_DUE_MISTAKE = 3; // Payee's request
}

enum PixTransactionChannelType {
    CPM = 0;    // Primary Channel, for Instant Payments
    CSM = 1;    // Secondary Channel, used Scheduled Payments.
}

message TransactionAmountDetail {
    wallet.PixAmountType amount_type = 1;
    double amount = 2;
}

message TransactionDetails {
    string end_to_end_id = 1;
    string reconciliation_id = 2; // txId
    PixKey pix_key = 3;
}

message TransactionRefundDetails {
    string end_to_end_id_original = 1;
    string end_to_end_id_refund = 2;
    string refund_reason_message = 3; // txId
    TransactionRefundType refund_type = 4;
}

enum PixInValidationReasonCode {
    PSP_INTERNAL_ERROR = 0; // JD => AB09
    BANK_ACCOUNT_INVALID = 1; // JD => AC03
    BANK_ACCOUNT_BLOCKED = 2; // JD => AC06
    BANK_ACCOUNT_CLOSED = 3; // JD => AC07
    BANK_ACCOUNT_TYPE_INCORRECT = 4; // JD => AC14
    BANK_ACCOUNT_TYPE_NOT_SUPPORTED = 5; // JD => AG03
    TRANSACTION_AMOUNT_LIMIT_EXCEEDED = 6; // JD => AM02
    TRANSACTION_REFUND_AMOUNT_EXCEEDED = 7; // JD => AM09
    PAYEE_DOCUMENT_ID_INCONSISTENT = 8; // JD => BE01
    PAYEE_DOCUMENT_ID_INVALID = 11; // JD => CH11
    QRCODE_REJECTED = 9; // JD => BE 17;
    RECONCILIATION_ID_INVALID = 10; // JD => BE15
    AUTOMATIC_PIX_CANCELLED = 12; // JD => CN01
    REJECTED_ORDER = 13; // JD => DS04
    TRANSACTION_DUPLICATED = 14; // JD => DUPL
    PROCESS_INTERNAL_ERROR = 15; // JD => ED05;
    FRAUD_ALARM = 16; // JD => FRAD
    PAYEE_UNDER_SANCTION = 17; // JD => RR04
    REFUND_ID_INVALID = 18; // JD => SL02
}