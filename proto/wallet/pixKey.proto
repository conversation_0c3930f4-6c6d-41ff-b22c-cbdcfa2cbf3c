syntax = "proto3";

package wallet;

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet";


/*
 * PixKeyCreateResult defines the possible outcomes when attempting to create a Pix Key.
 */
enum PixKeyCreateResult {
    CREATE_SUCCESS = 0;                  // The Pix Key was created successfully.
    KEY_EXISTS_OTHER_PERSON = 1;         // The Pix Key already exists and is owned by another person.
    KEY_EXISTS_OTHER_PARTICIPANT = 2;    // The Pix Key already exists and is linked to another financial institution.
}

/*
 * PixKeyType defines the valid types of Pix Keys.
 */
enum PixKeyType {
    CPF = 0;      // Brazilian Individual Taxpayer Registry, max. 11 digits.
    CNPJ = 1;     // Brazilian Company Taxpayer Registry, max. 14 digits.
    EMAIL = 2;    // Email address, max. 72 characters.
    PHONE = 3;    // Phone number, including country and area code (e.g., +5511987654321).
    EVP = 4;      // EVP - Virtual Payment Address - a random key generated by the Pix system.
}

/*
 * Pix<PERSON>ey represents the fundamental structure of a Pix Key.
 */
message PixKey {
    PixKeyType key_type = 1; // The type of the Pix Key (e.g., CPF, EMAIL).
    string key_value = 2;    // The actual value of the Pix Key (e.g., "123.456.789-00", "<EMAIL>").
}

/*
 * PixKeyCreateReason defines the reasons for creating a Pix Key.
 */
enum PixKeyCreateReason {
    CREATE_PIXKEY_REQUEST = 0;         // The Pix Key creation was initiated by a client request.
    CREATE_PIXKEY_RECONCILIATION = 6;  // The Pix Key creation is part of a reconciliation process.
}

/*
 * PixKeyUpdateReason defines the reasons for updating a Pix Key.
 */
enum PixKeyUpdateReason {
    CLIENT_UPDATE_REQUEST = 0;           // The Pix Key update was initiated by a client request.
    ACCOUNT_CHANGE = 2;                  // The Pix Key is being updated due to a change in the client's associated account number.
    RECONCILIATION_UPDATE = 6;           // The Pix Key update is part of a reconciliation process.
}

/*
 * PixKeyDeleteReason defines the reasons for deleting a Pix Key.
 */
enum PixKeyDeleteReason {
    DELETE_CLIENT_REQUEST = 0;             // The Pix Key deletion was initiated by a client request.
    DELETE_FRAUD = 4;                      // The Pix Key was deleted due to detected fraudulent activity.
    DELETE_RECONCILIATION = 6;             // The Pix Key deletion is part of a reconciliation process.
    DELETE_FEDERAL_REVENUE_VALIDATION = 8; // The Pix Key was deleted due to a validation by a government authority (e.g., Federal Revenue).
}

/*
 * PixKeyClaimStatus defines the possible statuses for a Pix Key claim process.
 */
enum PixKeyClaimStatus {
    CLAIM_OPEN = 0;      // Status whenever a pixClaim task is created.
    CLAIM_ACK = 1;       // Current PixKey Owner ACK he has received the claim.
    CLAIM_CONFIRMED = 2; // Current PixKey Owner agrees to give the PixKey away
    CLAIM_CANCELLED = 3; // Either Requester or Owner cancel the claim
    CLAIM_SUCCESS = 4;   // PixKey Owner agrees to give, and requester confirms
}

enum PixKeyBucketPolicy {
    ENTRIES_READ_USER_ANTISCAN = 0;
    ENTRIES_READ_USER_ANTISCAN_V2 = 1;
    ENTRIES_READ_PARTICIPANT_ANTISCAN = 2;
    ENTRIES_STATISTICS_READ = 3;
    ENTRIES_WRITE = 4;
    ENTRIES_UPDATE = 5;
    CLAIMS_READ = 6;
    CLAIMS_WRITE = 7;
    CLAIMS_LIST_WITH_ROLE = 8;
    CLAIMS_LIST_WITHOUT_ROLE = 9;
    SYNC_VERIFICATIONS_WRITE = 10;
    CIDS_FILES_WRITE = 11;
    CIDS_FILES_READ = 12;
    CIDS_EVENTS_LIST = 13;
    CIDS_ENTRIES_READ = 14;
    INFRACTION_REPORTS_READ = 15;
    INFRACTION_REPORTS_WRITE = 16;
    INFRACTION_REPORTS_LIST_WITH_ROLE = 17;
    INFRACTION_REPORTS_LIST_WITHOUT_ROLE = 18;
    KEYS_CHECK = 19;
    REFUNDS_READ = 20;
    REFUNDS_WRITE = 21;
    REFUND_LIST_WITH_ROLE = 22;
    REFUND_LIST_WITHOUT_ROLE = 23;
    FRAUD_MARKERS_READ = 24;
    FRAUD_MARKERS_WRITE = 25;
    FRAUD_MARKERS_LIST = 26;
    PERSONS_STATISTICS_READ = 27;
    POLICIES_READ = 28;
    POLICIES_LIST = 29;
}

message PixKeyPolicy {
    PixKeyBucketPolicy policy_name = 1;
    int32 token_available = 2;
    int32 token_capacity = 3;
    int32 token_refresh_amount = 4;
    int32 token_refresh_time = 5;
}