syntax = "proto3";

package wallet;

import "wallet/pixKey.proto";
import "wallet/banking.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet";

enum QRCodeResponseType {
    IMAGE_ONLY = 0;
    PAYLOAD_ONLY = 1;
    BOTH = 3;
}

enum QRCodeType {
    NOT_SET = 0;
    STATIC = 11;
    DYNAMIC_INSTANT_PAYMENT = 12;
    DYNAMIC_EXPIRE_PAYMENT = 13;
    COMPOSITE_PAYMENT = 14;
}

enum QRCodeBillingStatus {
    ACTIVE = 0;
    COMPLETED = 1;
    REMOVED_BY_RECIPIENT = 2;
    REMOVED_BY_PSP = 3;
}

message AdditionalData {
    string name = 1;
    string value = 2;
}

message StaticQRCodeData {
    double amount = 1;
    wallet.PixKey pix_key = 2;
    wallet.BankAccount payee_account = 3;
    wallet.BankAccountHolder payee_account_holder = 4;

    string city = 5;
    string category_code = 6;
    string receiver_reconciliation_id = 7;
}

message DynamicQRCodeData {
    int32 revision = 1;
    wallet.PixKey pix_key = 2;

    wallet.BankAccount payee_account = 3;
    wallet.BankAccountHolder payee_account_holder = 4;
    string payee_reconciliation_id = 5;                       // TxID for QrCode
    string category_code = 6;

    wallet.BankAccountHolder payer_account_holder = 7;
    string payer_request = 8;

    string city = 9;
    string postal_code = 10;

    // TODO: Dynamic QR Code with Expiration Date need more parameters (like discount)

    double original_amount = 11;
    double final_amount = 12;
    google.protobuf.Timestamp due_date = 13;
    int32 days_after_due = 14;
    int32 qrcode_expiration = 15;

    google.protobuf.Timestamp creation_datetime = 16;
    google.protobuf.Timestamp presentation_datetime = 17;
    string receiver_psp_url = 18;
    bool reusable = 19;                                     // Define if QrCode can be paid more than one time
    QRCodeBillingStatus status = 20;
    repeated AdditionalData additional_data = 21;
}