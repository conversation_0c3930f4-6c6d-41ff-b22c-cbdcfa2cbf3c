# JDPI Gateway Makefile

# 变量定义
APP_NAME := jdpi-gateway
VERSION := 1.0.0
BUILD_DIR := build
PROTO_DIR := proto
TEST_DIR := test
GO_FILES := $(shell find . -name "*.go" -type f)

# Go 相关变量
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod
GOFMT := $(GOCMD) fmt

# 构建标志
LDFLAGS := -ldflags "-X main.AppVersion=$(VERSION)"

# 默认目标
.PHONY: all
all: clean deps proto build

# 安装依赖
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# 生成protobuf代码
.PHONY: proto
proto:
	@echo "Generating protobuf code..."
	@if command -v protoc >/dev/null 2>&1; then \
		./scripts/generate_proto.sh; \
	else \
		echo "protoc not found, skipping proto generation"; \
		echo "Proto files are already generated and included in the repository"; \
	fi

# 构建应用
.PHONY: build
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME) ./cmd/main.go

# 运行应用（开发模式）
.PHONY: run
run: build
	@echo "Running $(APP_NAME) in development mode..."
	./$(BUILD_DIR)/$(APP_NAME) -config=config/config.yaml

# 运行应用（Mock模式）
.PHONY: run-mock
run-mock: build
	@echo "Running $(APP_NAME) in mock mode..."
	@echo "Creating mock config..."
	@mkdir -p config
	@echo 'server:' > config/config.yaml
	@echo '  host: "0.0.0.0"' >> config/config.yaml
	@echo '  port: 8080' >> config/config.yaml
	@echo '' >> config/config.yaml
	@echo 'jd:' >> config/config.yaml
	@echo '  base_url: "https://mock.jd.api.com"' >> config/config.yaml
	@echo '  grant_type: "client_credentials"' >> config/config.yaml
	@echo '  scope: "jdpi"' >> config/config.yaml
	@echo '  mock_mode: true' >> config/config.yaml
	@echo '  timeout_seconds: 30' >> config/config.yaml
	@echo '' >> config/config.yaml
	@echo 'kms:' >> config/config.yaml
	@echo '  region: "us-east-1"' >> config/config.yaml
	@echo '  client_id_key: "jd/client_id"' >> config/config.yaml
	@echo '  client_secret_key: "jd/client_secret"' >> config/config.yaml
	@echo '' >> config/config.yaml
	@echo 'log:' >> config/config.yaml
	@echo '  level: "info"' >> config/config.yaml
	@echo '  format: "console"' >> config/config.yaml
	@echo '  output_path: "stdout"' >> config/config.yaml
	./$(BUILD_DIR)/$(APP_NAME) -config=config/config.yaml

# 测试
.PHONY: test
test:
	@echo "Running unit tests..."
	$(GOTEST) -v -race -coverpkg=./internal/... -covermode=atomic -coverprofile=coverage.out ./$(TEST_DIR)/...
	@echo "Test coverage:"
	@$(GOCMD) tool cover -func=coverage.out | tail -1
	@echo "Generating coverage report..."
	@$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 测试覆盖率
.PHONY: test-coverage
test-coverage: test
	@echo "Generating coverage report..."
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"


# 代码格式化
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GOFMT) ./...

# 代码检查
.PHONY: lint
lint:
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not found, skipping lint"; \
	fi

# 清理
.PHONY: clean
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# 安装工具
.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	$(GOGET) google.golang.org/protobuf/cmd/protoc-gen-go@latest
	$(GOGET) google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	@if ! command -v golangci-lint >/dev/null 2>&1; then \
		echo "Installing golangci-lint..."; \
		curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin v1.54.2; \
	fi

# Docker构建
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

# Docker运行
.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 --rm $(APP_NAME):latest

# 显示版本
.PHONY: version
version:
	@echo "$(APP_NAME) version $(VERSION)"

# 显示帮助
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all           - Clean, install deps, generate proto, and build"
	@echo "  deps          - Install Go dependencies"
	@echo "  proto         - Generate protobuf code"
	@echo "  build         - Build the application"
	@echo "  run           - Run the application in development mode"
	@echo "  run-mock      - Run the application in mock mode"
	@echo "  test          - Run unit tests"
	@echo "  test-coverage - Run tests with coverage report"
	@echo "  fmt           - Format Go code"
	@echo "  lint          - Run linter"
	@echo "  clean         - Clean build artifacts"
	@echo "  install-tools - Install development tools"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  version       - Show version"
	@echo "  help          - Show this help"
