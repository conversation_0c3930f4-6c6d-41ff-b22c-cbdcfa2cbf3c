#!/bin/bash

MODULE_PATH="gitlab.pagsmile.com/wallet/ew_pix_adapter_proto"
PROTO_ROOT_DIR="./proto" # Root directory where your .proto files are, including subdirectories
GO_OUT_DIR="gen/go" # New directory for generated Go code

echo "Checking dependencies..."

command_exists () {
  type "$1" &> /dev/null ;
}

if ! command_exists protoc; then
  echo "Error: The 'protoc' compiler was not found."
  echo "Please install it following the instructions at: https://grpc.io/docs/protoc-installation/"
  exit 1
fi
echo "'protoc' found."

export PATH=$PATH:$(go env GOPATH)/bin

if ! command_exists protoc-gen-go; then
  echo "Plugin 'protoc-gen-go' not found. Installing..."
  go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
  if [ $? -ne 0 ]; then
    echo "Error: Failed to install 'protoc-gen-go'. Check your connection and permissions."
    exit 1
  fi
fi
echo "'protoc-gen-go' found/installed."

if ! command_exists protoc-gen-go-grpc; then
  echo "Plugin 'protoc-gen-go-grpc' not found. Installing..."
  go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
  if [ $? -ne 0 ]; then
    echo "Error: Failed to install 'protoc-gen-go-grpc'. Check your connection and permissions."
    exit 1
  fi
fi
echo "'protoc-gen-go-grpc' found/installed."

echo "Dependencies checked."

echo "Generating Go code from all .proto files in ${PROTO_ROOT_DIR} and its subdirectories..."
echo "Generated code will be saved in ${GO_OUT_DIR}/"

# Check if the proto root directory exists
if [ ! -d "$PROTO_ROOT_DIR" ]; then
    echo "Error: Proto root directory '${PROTO_ROOT_DIR}' not found."
    echo "Make sure to run this script from the root of your module (mywalletmodule)."
    exit 1
fi

mkdir -p "${GO_OUT_DIR}"

ALL_PROTO_FILES=$(find "${PROTO_ROOT_DIR}" -name "*.proto")

if [ -z "$ALL_PROTO_FILES" ]; then
    echo "No .proto files found in '${PROTO_ROOT_DIR}'. Nothing to compile."
    exit 0
fi

find "$PROTO_ROOT_DIR" -name "*.proto" | xargs -I {} protoc \
  --proto_path="$PROTO_ROOT_DIR" \
  --go_out="$GO_OUT_DIR" \
  --go_opt=paths=source_relative \
  --go-grpc_out="$GO_OUT_DIR" \
  --go-grpc_opt=paths=source_relative \
  {}

if [ $? -eq 0 ]; then
  echo "Go code generated successfully in ${GO_OUT_DIR}/."
else
  echo "Error: Failed to generate Go code from Protobuf."
  exit 1
fi

echo "Protobuf code generation process completed."

go mod tidy

echo "mod tidy script completed"