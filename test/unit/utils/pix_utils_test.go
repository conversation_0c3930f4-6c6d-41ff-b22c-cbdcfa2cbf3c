package utils_test

import (
	"testing"

	"jdpi-gateway/internal/utils"

	"github.com/stretchr/testify/assert"

	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// TestConvertJDToQRCodeResponseType tests QR code response type conversion from JD format
func TestConvertJDToQRCodeResponseType(t *testing.T) {
	tests := []struct {
		name     string
		input    int
		expected wallet.QRCodeResponseType
	}{
		{
			name:     "Image only",
			input:    0,
			expected: wallet.QRCodeResponseType_IMAGE_ONLY,
		},
		{
			name:     "Payload only",
			input:    1,
			expected: wallet.QRCodeResponseType_PAYLOAD_ONLY,
		},
		{
			name:     "Both image and payload",
			input:    3,
			expected: wallet.QRCodeResponseType_BOTH,
		},
		{
			name:     "Unknown type defaults to both",
			input:    999,
			expected: wallet.QRCodeResponseType_BOTH,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ConvertJDToQRCodeResponseType(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestConvertQRCodeResponseTypeToJD tests QR code response type conversion to JD format
func TestConvertQRCodeResponseTypeToJD(t *testing.T) {
	tests := []struct {
		name     string
		input    wallet.QRCodeResponseType
		expected int
	}{
		{
			name:     "Image only",
			input:    wallet.QRCodeResponseType_IMAGE_ONLY,
			expected: 0,
		},
		{
			name:     "Payload only",
			input:    wallet.QRCodeResponseType_PAYLOAD_ONLY,
			expected: 1,
		},
		{
			name:     "Both image and payload",
			input:    wallet.QRCodeResponseType_BOTH,
			expected: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ConvertQRCodeResponseTypeToJD(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestConvertJDPixKeyTypeToEnum tests PIX key type conversion from JD format
func TestConvertJDPixKeyTypeToEnum(t *testing.T) {
	tests := []struct {
		name     string
		input    int
		expected wallet.PixKeyType
	}{
		{
			name:     "CPF",
			input:    1,
			expected: wallet.PixKeyType(1), // Direct conversion
		},
		{
			name:     "CNPJ",
			input:    2,
			expected: wallet.PixKeyType(2), // Direct conversion
		},
		{
			name:     "Email",
			input:    3,
			expected: wallet.PixKeyType(3), // Direct conversion
		},
		{
			name:     "Phone",
			input:    4,
			expected: wallet.PixKeyType(4), // Direct conversion
		},
		{
			name:     "Unknown type defaults to EVP",
			input:    999,
			expected: wallet.PixKeyType_EVP,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ConvertJDPixKeyTypeToEnum(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestConvertPixKeyTypeToJD tests PIX key type conversion to JD format
func TestConvertPixKeyTypeToJD(t *testing.T) {
	tests := []struct {
		name     string
		input    wallet.PixKeyType
		expected int
	}{
		{
			name:     "CPF",
			input:    wallet.PixKeyType_CPF,
			expected: int(wallet.PixKeyType_CPF),
		},
		{
			name:     "CNPJ",
			input:    wallet.PixKeyType_CNPJ,
			expected: int(wallet.PixKeyType_CNPJ),
		},
		{
			name:     "Email",
			input:    wallet.PixKeyType_EMAIL,
			expected: int(wallet.PixKeyType_EMAIL),
		},
		{
			name:     "Phone",
			input:    wallet.PixKeyType_PHONE,
			expected: int(wallet.PixKeyType_PHONE),
		},
		{
			name:     "EVP",
			input:    wallet.PixKeyType_EVP,
			expected: int(wallet.PixKeyType_EVP),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ConvertPixKeyTypeToJD(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestValidateQRCodeAmount tests QR code amount validation
func TestValidateQRCodeAmount(t *testing.T) {
	tests := []struct {
		name      string
		amount    float64
		expectErr bool
	}{
		{
			name:      "Valid positive amount",
			amount:    100.50,
			expectErr: false,
		},
		{
			name:      "Valid zero amount",
			amount:    0.0,
			expectErr: false,
		},
		{
			name:      "Invalid negative amount",
			amount:    -10.0,
			expectErr: true,
		},
		{
			name:      "Valid small amount",
			amount:    0.01,
			expectErr: false,
		},
		{
			name:      "Valid large amount",
			amount:    999999.99,
			expectErr: false,
		},
		{
			name:      "Invalid amount exceeds maximum",
			amount:    1000000000.00,
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := utils.ValidateQRCodeAmount(tt.amount)
			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestGetCategoryCodeForQRCode tests category code retrieval for QR codes
func TestGetCategoryCodeForQRCode(t *testing.T) {
	result := utils.GetCategoryCodeForQRCode()
	assert.Equal(t, "0014", result)
}

// TestParseDocumentID tests document ID parsing
func TestParseDocumentID(t *testing.T) {
	tests := []struct {
		name      string
		input     string
		expected  int64
		expectErr bool
	}{
		{
			name:      "Valid CPF",
			input:     "123.456.789-01",
			expected:  12345678901,
			expectErr: false,
		},
		{
			name:      "Valid CNPJ",
			input:     "12.345.678/0001-90",
			expected:  12345678000190,
			expectErr: false,
		},
		{
			name:      "Clean number",
			input:     "12345678901",
			expected:  12345678901,
			expectErr: false,
		},
		{
			name:      "Empty string",
			input:     "",
			expected:  0,
			expectErr: true,
		},
		{
			name:      "Invalid format",
			input:     "abc123",
			expected:  0,
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ParseDocumentID(tt.input)
			if tt.expectErr {
				assert.Equal(t, int64(0), result)
			} else {
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

// TestParseInt64 tests int64 parsing
func TestParseInt64(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected int64
	}{
		{
			name:     "Valid number",
			input:    "12345",
			expected: 12345,
		},
		{
			name:     "Empty string",
			input:    "",
			expected: 0,
		},
		{
			name:     "Invalid format",
			input:    "abc",
			expected: 0,
		},
		{
			name:     "Large number",
			input:    "9223372036854775807",
			expected: 9223372036854775807,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ParseInt64(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestParseInt tests int parsing
func TestParseInt(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected int
	}{
		{
			name:     "Valid number",
			input:    "123",
			expected: 123,
		},
		{
			name:     "Empty string",
			input:    "",
			expected: 0,
		},
		{
			name:     "Invalid format",
			input:    "abc",
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ParseInt(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
