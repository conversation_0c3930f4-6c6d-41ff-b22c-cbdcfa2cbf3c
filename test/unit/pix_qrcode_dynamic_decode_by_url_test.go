package unit

import (
	"testing"

	"jdpi-gateway/internal/model"

	"github.com/stretchr/testify/assert"
)

// TestQRCodeDynamicDecodeByUrlJDRequest_JSONMarshaling tests JSON marshaling of the request
func TestQRCodeDynamicDecodeByUrlJDRequest_JSONMarshaling(t *testing.T) {
	// Create test request
	req := &model.QRCodeDynamicDecodeByUrlJDRequest{
		URLPayloadJSON:   "example.com/pix/8b3da2f3-9a41-40d1-a91a-bd93113bd441",
		CityCode:         "3550308",
		PresentationDate: "2024-01-30",
	}

	// Test that the struct has the correct JSON tags
	assert.Equal(t, "example.com/pix/8b3da2f3-9a41-40d1-a91a-bd93113bd441", req.URLPayloadJSON)
	assert.Equal(t, "3550308", req.CityCode)
	assert.Equal(t, "2024-01-30", req.PresentationDate)

	t.Logf("✅ QRCodeDynamicDecodeByUrlJDRequest structure is correct")
	t.Logf("   URLPayloadJSON: %s", req.URLPayloadJSON)
	t.Logf("   CityCode: %s", req.CityCode)
	t.Logf("   PresentationDate: %s", req.PresentationDate)
}

// TestQRCodeDynamicDecodeByUrlJDRequest_MinimalRequest tests minimal request with only required fields
func TestQRCodeDynamicDecodeByUrlJDRequest_MinimalRequest(t *testing.T) {
	// Create minimal test request (only required field)
	req := &model.QRCodeDynamicDecodeByUrlJDRequest{
		URLPayloadJSON: "example.com/pix/minimal-test",
	}

	// Test that optional fields can be empty
	assert.Equal(t, "example.com/pix/minimal-test", req.URLPayloadJSON)
	assert.Equal(t, "", req.CityCode)
	assert.Equal(t, "", req.PresentationDate)

	t.Logf("✅ Minimal QRCodeDynamicDecodeByUrlJDRequest is valid")
	t.Logf("   URLPayloadJSON: %s", req.URLPayloadJSON)
	t.Logf("   CityCode: %s (empty, optional)", req.CityCode)
	t.Logf("   PresentationDate: %s (empty, optional)", req.PresentationDate)
}
