// Package integration contains integration tests for PIX Dict operations
package integration

import (
	"context"
	"testing"
	"time"

	"jdpi-gateway/internal/model"
	"jdpi-gateway/test/integration/fixture"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	// External protocol imports
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// Test_shouldReturnValidationErrorWhenCreatePixKeyGivenInvalidRequest tests parameter validation failure
func Test_shouldReturnValidationErrorWhenCreatePixKeyGivenInvalidRequest(t *testing.T) {
	// Setup test server with default mocks
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create test request with missing required fields (should cause validation error)
	req := &pixrequest.PixKeyCreateRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "0e85f17b18b048aa93c4116014c5ca8e",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		// Missing PixKey, BankAccount, BankAccountHolder - should cause validation error
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyCreate(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyCreate should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for invalid request")
	require.Nil(t, resp.GetResponse(), "Should not get success response for invalid request")

	// Verify error details
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "INVALID_PARAMETERS", errorResp.ErrorSubcode, "Error subcode should indicate invalid parameters")
	assert.Contains(t, errorResp.ErrorMessage, "Parameter validation failed", "Error message should mention validation failure")

	t.Logf("✅ Validation error test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}

// Test_shouldReturnSuccessResponseWhenCreatePixKeyGivenValidRequestAndJDSuccess tests successful PIX key creation
func Test_shouldReturnSuccessResponseWhenCreatePixKeyGivenValidRequestAndJDSuccess(t *testing.T) {
	// Setup test server with custom success response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return success response
	successResponse := &model.CreatePixKeyJDResponse{
		PixKey:                       "<EMAIL>",
		PixKeyCreationDatetime:       "2025-08-15T10:06:59.978Z",
		PixKeyOwnershipStartDatetime: "2025-08-15T10:06:59.975Z",
		ClaimOpeningDatetime:         "2025-08-15T10:06:59.975Z",
	}
	mockPixDictClient.SetResponse("CreatePixKey", &fixture.APIResponse{
		Success:   successResponse,
		ShouldErr: false,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.PixKeyCreateRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "0e85f17b18b048aa93c4116014c5ca8e",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EMAIL,
			KeyValue: "<EMAIL>",
		},
		BankAccount: &wallet.BankAccount{
			Ispb:                   ********,
			BranchCode:             "0001",
			AccountNumber:          "*********",
			AccountType:            wallet.BankAccountType_CHECKING_ACCOUNT,
			Status:                 "ACTIVE",
			AccountOpeningDatetime: timestamppb.New(time.Now()),
		},
		BankAccountHolder: &wallet.BankAccountHolder{
			HolderType:     wallet.AccountHolderType_LEGAL,
			HolderName:     "Gordon CNPJ DEV XLLL",
			HolderNickname: "",
			DocumentId:     "**************",
		},
		Reason: wallet.PixKeyCreateReason_CREATE_PIXKEY_REQUEST,
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyCreate(ctx, req)

	// Verify no error occurred
	require.NoError(t, err, "PixKeyCreate should not return an error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got a success response
	successResp := resp.GetResponse()
	require.NotNil(t, successResp, "Should get success response")
	require.Nil(t, resp.GetError(), "Should not get error response")

	// Verify PIX key information
	assert.NotNil(t, successResp.PixKey, "Response should contain PIX key information")
	assert.Equal(t, wallet.PixKeyType_EMAIL, successResp.PixKey.KeyType, "PIX key type should match")
	assert.Equal(t, "<EMAIL>", successResp.PixKey.KeyValue, "PIX key value should match")

	// Verify timestamps
	assert.NotNil(t, successResp.KeyCreationDatetime, "Key creation datetime should be present")
	assert.NotNil(t, successResp.KeyPossessionStartDatetime, "Key possession start datetime should be present")

	// Verify the timestamps match the expected mock response
	expectedCreationTime, err := time.Parse(time.RFC3339, "2025-08-15T10:06:59.978Z")
	require.NoError(t, err, "Should parse expected creation time")

	expectedPossessionTime, err := time.Parse(time.RFC3339, "2025-08-15T10:06:59.975Z")
	require.NoError(t, err, "Should parse expected possession time")

	actualCreationTime := successResp.KeyCreationDatetime.AsTime()
	actualPossessionTime := successResp.KeyPossessionStartDatetime.AsTime()

	assert.Equal(t, expectedCreationTime, actualCreationTime, "Creation datetime should match mock response")
	assert.Equal(t, expectedPossessionTime, actualPossessionTime, "Possession start datetime should match mock response")

	t.Logf("✅ Success response test completed successfully")
	t.Logf("   PIX Key: %s", successResp.PixKey.KeyValue)
	t.Logf("   Creation Time: %s", actualCreationTime.Format(time.RFC3339))
	t.Logf("   Possession Time: %s", actualPossessionTime.Format(time.RFC3339))
}

// Test_shouldReturnErrorResponseWhenCreatePixKeyGivenValidRequestButJDFailure tests JD API failure handling
func Test_shouldReturnErrorResponseWhenCreatePixKeyGivenValidRequestButJDFailure(t *testing.T) {
	// Setup test server with custom error response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return JD API error
	jdError := &model.JDAPIError{
		Message:       "PIX key already exists",
		OriginalError: "The provided PIX key is already registered in the system",
		StatusCode:    400,
		JDError: &model.JDError{
			Codigo:   "PIX_KEY_ALREADY_EXISTS",
			Mensagem: "PIX key already exists in the dictionary",
		},
	}
	mockPixDictClient.SetResponse("CreatePixKey", &fixture.APIResponse{
		Error:     jdError,
		ShouldErr: true,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.PixKeyCreateRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "0e85f17b18b048aa93c4116014c5ca8e",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EMAIL,
			KeyValue: "<EMAIL>",
		},
		BankAccount: &wallet.BankAccount{
			Ispb:                   ********,
			BranchCode:             "0001",
			AccountNumber:          "*********",
			AccountType:            wallet.BankAccountType_CHECKING_ACCOUNT,
			Status:                 "ACTIVE",
			AccountOpeningDatetime: timestamppb.New(time.Now()),
		},
		BankAccountHolder: &wallet.BankAccountHolder{
			HolderType:     wallet.AccountHolderType_LEGAL,
			HolderName:     "Gordon CNPJ DEV XLLL",
			HolderNickname: "",
			DocumentId:     "**************",
		},
		Reason: wallet.PixKeyCreateReason_CREATE_PIXKEY_REQUEST,
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyCreate(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyCreate should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for JD API failure")
	require.Nil(t, resp.GetResponse(), "Should not get success response for JD API failure")

	// Verify error details match JD API error
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "PIX_KEY_ALREADY_EXISTS", errorResp.ErrorSubcode, "Error subcode should match JD error code")
	assert.Contains(t, errorResp.ErrorMessage, "JD API failed", "Error message should contain JD error message")

	t.Logf("✅ JD API failure test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}
