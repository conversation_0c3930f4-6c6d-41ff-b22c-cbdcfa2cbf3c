// Package integration contains integration tests for PIX QRCode operations
package integration

import (
	"context"
	"testing"

	"jdpi-gateway/internal/model"
	"jdpi-gateway/test/integration/fixture"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	// External protocol imports
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// Test_shouldReturnValidationErrorWhenDecodeDynamicQRCodeByUrlGivenInvalidRequest tests parameter validation failure
func Test_shouldReturnValidationErrorWhenDecodeDynamicQRCodeByUrlGivenInvalidRequest(t *testing.T) {
	// Setup test server with default mocks
	logger := zap.NewNop()
	mockPixQRCodeClient := fixture.NewMockPixQRCodeClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	testServer := fixture.SetupQRCodeTestServer(t, mockPixQRCodeClient, mockAuthService)
	defer testServer.Cleanup()

	// Create test request with missing required fields (should cause validation error)
	req := &pixrequest.QRCodeDynamicDecodeByUrlRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "0e85f17b18b048aa93c4116014c5ca8e",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		// Missing required fields: EndToEndId, PayerDocumentId, QrcodePayloadUrl - should cause validation error
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.QRCodeDynamicDecodeByUrl(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "DecodeDynamicQRCodeByUrl should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for invalid request")
	require.Nil(t, resp.GetResponse(), "Should not get success response for invalid request")

	// Verify error details
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "INVALID_PARAMETERS", errorResp.ErrorSubcode, "Error subcode should indicate invalid parameters")
	assert.Contains(t, errorResp.ErrorMessage, "Parameter validation failed", "Error message should mention validation failure")

	t.Logf("✅ Validation error test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}

// Test_shouldReturnSuccessResponseWhenDecodeDynamicQRCodeByUrlGivenValidRequestAndJDSuccess tests successful dynamic QR code decoding
func Test_shouldReturnSuccessResponseWhenDecodeDynamicQRCodeByUrlGivenValidRequestAndJDSuccess(t *testing.T) {
	// Setup test server with custom success response
	logger := zap.NewNop()
	mockPixQRCodeClient := fixture.NewMockPixQRCodeClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return success response
	successResponse := &model.QRCodeDynamicDecodeByUrlJDResponse{
		EndToEndID: "E12345678901234567890123456789012",
		QRCodeType: 12, // DYNAMIC_INSTANT_PAYMENT QR code type
		DynamicQRCodeData: &model.DynamicQRCodeDataJD{
			Revision:                 1,
			Ispb:                     48756121,
			PixKey:                   "<EMAIL>",
			ReceiverReconciliationID: "test-tx-id",
			CategoryCode:             "0014",
			ReceiverName:             "Test User",
			ReceiverPersonType:       1,
			ReceiverCPFCNPJ:          12345678901,
			City:                     "São Paulo",
			PostalCode:               "01234567",
			OriginalAmount:           100.50,
			QRExpiration:             3600,
			Reusable:                 false,
			Status:                   1,
		},
	}
	mockPixQRCodeClient.SetResponse("DecodeDynamicQRCodeByUrl", &fixture.APIResponse{
		Success:   successResponse,
		ShouldErr: false,
	})

	testServer := fixture.SetupQRCodeTestServer(t, mockPixQRCodeClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.QRCodeDynamicDecodeByUrlRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "0e85f17b18b048aa93c4116014c5ca8e",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		EndToEndId:       "E12345678901234567890123456789012",
		PayerDocumentId:  "12345678901",
		QrcodePayloadUrl: "https://example.com/qrcode/payload/123",
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.QRCodeDynamicDecodeByUrl(ctx, req)

	// Verify no error occurred
	require.NoError(t, err, "DecodeDynamicQRCodeByUrl should not return an error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got a success response
	successResp := resp.GetResponse()
	require.NotNil(t, successResp, "Should get success response")
	require.Nil(t, resp.GetError(), "Should not get error response")

	// Verify QR code data
	assert.Equal(t, "E12345678901234567890123456789012", successResp.EndToEndId, "End-to-end ID should match")
	assert.Equal(t, wallet.QRCodeType_DYNAMIC_INSTANT_PAYMENT, successResp.QrcodeType, "QR code type should match")

	t.Logf("✅ Success response test completed successfully")
	t.Logf("   End-to-End ID: %s", successResp.EndToEndId)
	t.Logf("   QR Code Type: %s", successResp.QrcodeType.String())
}

// Test_shouldReturnErrorResponseWhenDecodeDynamicQRCodeByUrlGivenValidRequestButJDFailure tests JD API failure handling
func Test_shouldReturnErrorResponseWhenDecodeDynamicQRCodeByUrlGivenValidRequestButJDFailure(t *testing.T) {
	// Setup test server with custom error response
	logger := zap.NewNop()
	mockPixQRCodeClient := fixture.NewMockPixQRCodeClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return JD API error
	jdError := &model.JDAPIError{
		Message:       "Dynamic QR code decode failed",
		OriginalError: "The provided URL is invalid or expired",
		StatusCode:    400,
		JDError: &model.JDError{
			Codigo:   "INVALID_URL",
			Mensagem: "The QR code URL is invalid or has expired",
		},
	}
	mockPixQRCodeClient.SetResponse("DecodeDynamicQRCodeByUrl", &fixture.APIResponse{
		Error:     jdError,
		ShouldErr: true,
	})

	testServer := fixture.SetupQRCodeTestServer(t, mockPixQRCodeClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.QRCodeDynamicDecodeByUrlRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "0e85f17b18b048aa93c4116014c5ca8e",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		EndToEndId:       "E12345678901234567890123456789012",
		PayerDocumentId:  "12345678901",
		QrcodePayloadUrl: "https://example.com/qrcode/payload/invalid",
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.QRCodeDynamicDecodeByUrl(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "DecodeDynamicQRCodeByUrl should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for JD API failure")
	require.Nil(t, resp.GetResponse(), "Should not get success response for JD API failure")

	// Verify error details match JD API error
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "INVALID_URL", errorResp.ErrorSubcode, "Error subcode should match JD error code")
	assert.Contains(t, errorResp.ErrorMessage, "JD API failed", "Error message should contain JD error message")

	t.Logf("✅ JD API failure test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}
