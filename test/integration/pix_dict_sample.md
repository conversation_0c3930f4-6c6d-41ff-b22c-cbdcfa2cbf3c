## JD API Error Response Sample

```json
{
  "codigo": "502",
  "mensagem": "CNPJ informado é inválido"
}
```

```json
{
  "codigo": "EntryKeyOwnedByDifferent<PERSON>erson",
  "mensagem": "Já existe vínculo para essa chave mas ela é possuída por outra pessoa. Indica-se queseja feita uma reivindicação de posse.",
  "idCorrelacao": "E00038166201907261559y6j6mt230pi"
}
```

## PIX Dict Integration Test Scenarios

### Create Pix Key

- Request

```json
{
  "header": {
    "idempotence_id": "0e85f17b18b048aa93c4116014c5ca8e",
    "channel": "A55_JD"
  },
  "ispb": ********,
  "pix_key": {
    "key_type": "EMAIL",
    "key_value": "<EMAIL>"
  },
  "bank_account": {
    "ispb": ********,
    "branch_code": "0001",
    "account_number": "*********",
    "account_type": "CHECKING_ACCOUNT"
  },
  "bank_account_holder": {
    "holder_type": "LEGAL",
    "holder_name": "Gordon CNPJ DEV XLLL",
    "holder_nickname": "",
    "document_id": "**************"
  },
  "reason": "CREATE_PIXKEY_REQUEST"
}
```

- JD Response

```json
{
  "chave": "<EMAIL>",
  "dtHrCriacaoChave": "2025-08-15T10:06:59.978Z",
  "dtHrInicioPosseChave": "2025-08-15T10:06:59.975Z"
}
```

- gRPC Response

```json
{
  "response": {
    "pixKey": {
      "keyType": "EMAIL",
      "keyValue": "<EMAIL>"
    },
    "keyCreationDatetime": "2025-08-15T10:06:59.978Z",
    "keyPossessionStartDatetime": "2025-08-15T10:06:59.975Z"
  }
}
```

### Update Pix Key

- Request

```json
{
  "header": {
    "idempotence_id": "0e85f17b18b048aa93c4116014c5ca8e",
    "channel": "A55_JD"
  },
  "ispb": ********,
  "pix_key": {
    "key_type": "EMAIL",
    "key_value": "<EMAIL>"
  },
  "bank_account": {
    "ispb": ********,
    "branch_code": "0001",
    "account_number": "*********",
    "account_type": "CHECKING_ACCOUNT"
  },
  "bank_account_holder": {
    "holder_type": "LEGAL",
    "holder_name": "Gordon CNPJ DEV XLLL",
    "holder_nickname": "",
    "document_id": "**************"
  },
  "reason": "UPDATE_PIX_KEY_ACCOUNT_CHANGE"
}
```

- JD Response

```json
{
  "chave": "<EMAIL>",
  "dtHrCriacaoChave": "2025-08-15T10:06:59.978Z",
  "dtHrInicioPosseChave": "2025-08-15T10:06:59.975Z"
}
```

- gRPC Response

```json
{
  "response": {
    "pixKey": {
      "keyType": "EMAIL",
      "keyValue": "<EMAIL>"
    },
    "keyCreationDatetime": "2025-08-15T10:06:59.978Z",
    "keyPossessionStartDatetime": "2025-08-15T10:06:59.975Z"
  }
}
```

### Get Pix Key

- Request

```json
{
  "header": {
    "idempotence_id": "147b46bf2862400dafbbc1897766316d",
    "channel": "A55_JD"
  },
  "ispb": ********,
  "e2e_id": "a6b6ce2e3b42474dba5920d9b9db4b2e",
  "requester_document_id": "**************",
  "pix_key": {
    "key_type": "EVP",
    "key_value": "8a85633a-3cf9-4187-9717-f87bff3ab33e"
  }
}
```

- JD Response

```json
{
  "chave": "8a85633a-3cf9-4187-9717-f87bff3ab33e",
  "tpChave": 4,
  "ispb": "********",
  "nrAgencia": "1",
  "nrConta": "1980226",
  "tpConta": 0,
  "tpPessoa": 1,
  "cpfCnpj": "**************",
  "nome": "Gordon CNPJ DEV XLLL",
  "dtHrCriacaoChave": "2025-08-14T13:31:04.544Z",
  "dtHrInicioPosseChave": "2025-08-14T13:31:04.544Z",
  "dtHrAberturaConta": "2025-08-14T13:31:04.379Z",
  "endToEndId": "a6b6ce2e3b42474dba5920d9b9db4b2e"
}
```

- gRPC Response

```json
{
  "response": {
    "pixKey": {
      "keyType": "EVP",
      "keyValue": "8a85633a-3cf9-4187-9717-f87bff3ab33e"
    },
    "bankAccount": {
      "ispb": ********,
      "branchCode": "1",
      "accountNumber": "1980226",
      "status": "ACTIVE",
      "accountOpeningDatetime": "2025-08-14T13:31:04.379Z"
    },
    "bankAccountHolder": {
      "holderType": "LEGAL",
      "holderName": "Gordon CNPJ DEV XLLL",
      "documentId": "**************"
    }
  }
}
```

### Verify Pix Key Existence

- Request

```json
{
  "header": {
    "idempotence_id": "1aaf1ef151274db59118262791d4e47f",
    "channel": "A55_JD"
  },
  "ispb": ********,
  "keys": [
    {
      "key_type": "EVP",
      "key_value": "********-6a62-4296-9761-b5cf940ac103"
    },
    {
      "key_type": "EMAIL",
      "key_value": "<EMAIL>"
    },
    {
      "key_type": "EVP",
      "key_value": "********-6a62-4296-9761-b5cf940ac102"
    }
  ]
}
```

- JD Response

```json
{
  "dtHrRetornoDict": "2025-08-15T10:45:23.914Z",
  "idCorrelacao": "B20250815074522588********48D608",
  "chavesVerificadas": [
    {
      "chave": "********-6a62-4296-9761-b5cf940ac103",
      "existeDict": false
    },
    {
      "chave": "<EMAIL>",
      "existeDict": true
    },
    {
      "chave": "********-6a62-4296-9761-b5cf940ac102",
      "existeDict": false
    }
  ]
}
```

- gRPC Response

```json
{
  "response": {
    "existentPixKey": [
      {
        "keyType": "EMAIL",
        "keyValue": "<EMAIL>"
      }
    ]
  }
}
```

### Delete Pix Key

- Request

```json
{
  "header": {
    "idempotence_id": "b1c5e0022e5543dc88c50084ccae82fc",
    "channel": "A55_JD"
  },
  "ispb": ********,
  "pix_key": {
    "key_type": "EVP",
    "key_value": "********-6a62-4296-9761-b5cf940ac103"
  },
  "reason": "DELETE_CLIENT_REQUEST"
}
```

- JD Response

```json
{
  "chave": "********-6a62-4296-9761-b5cf940ac103"
}
```

- gRPC Response

```json
{
  "response": {
    "pixKey": {
      "keyType": "EVP",
      "keyValue": "********-6a62-4296-9761-b5cf940ac103"
    }
  }
}
```

### List Pix Keys

- Request

```json
{
  "header": {
    "idempotence_id": "86a985f9f80f4811bb03376cdfe19dba",
    "channel": "A55_JD"
  },
  "ispb": ********,
  "bank_account": {
    "ispb": ********,
    "branch_code": "0001",
    "account_number": "*********",
    "account_type": "CHECKING_ACCOUNT"
  },
  "bank_account_holder": {
    "holder_type": "LEGAL",
    "holder_name": "Gordon CNPJ DEV XLLL",
    "holder_nickname": "",
    "document_id": "**************"
  }
}
```

- JD Response
```json
{
  "dtHrJdPi": "2025-08-15T10:07:41.581Z",
  "chavesAssociadas": [
    {
      "tpChave": 4,
      "chave": "6adbd0af-c88f-430a-938e-e3856e51e451",
      "nome": "Gordon CNPJ DEV XLLL",
      "dtHrCriacaoChave": "2025-08-14T14:33:26.399Z",
      "dtHrInicioPosseChave": "2025-08-14T14:33:26.399Z"
    },
    {
      "tpChave": 4,
      "chave": "8a85633a-3cf9-4187-9717-f87bff3ab33e",
      "nome": "Gordon CNPJ DEV XLLL",
      "dtHrCriacaoChave": "2025-08-14T13:31:04.544Z",
      "dtHrInicioPosseChave": "2025-08-14T13:31:04.544Z"
    },
    {
      "tpChave": 2,
      "chave": "<EMAIL>",
      "nome": "Gordon CNPJ DEV XLLL",
      "dtHrCriacaoChave": "2025-08-15T10:06:59.978Z",
      "dtHrInicioPosseChave": "2025-08-15T10:06:59.975Z"
    }
  ]
}
```

- gRPC Response
```json
{
  "response": {
    "pixKeys": [
      {
        "keyType": "EVP",
        "keyValue": "6adbd0af-c88f-430a-938e-e3856e51e451"
      },
      {
        "keyType": "EVP",
        "keyValue": "8a85633a-3cf9-4187-9717-f87bff3ab33e"
      },
      {
        "keyType": "EMAIL",
        "keyValue": "<EMAIL>"
      }
    ]
  }
}
```


