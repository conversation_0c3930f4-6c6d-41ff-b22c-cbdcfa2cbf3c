package integration

import (
	"context"
	"testing"
	"time"

	"jdpi-gateway/test/integration/fixture"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// Test_shouldReturnValidationErrorWhenPixOutConfirmGivenInvalidRequest tests parameter validation failure
func Test_shouldReturnValidationErrorWhenPixOutConfirmGivenInvalidRequest(t *testing.T) {
	// Setup test server
	testServer := fixture.NewTestServer(t)
	defer testServer.Close()

	// Create gRPC client
	client := testServer.CreatePixPaymentServiceClient()

	// Test case: Missing required fields
	req := &pixrequest.PixOutConfirmRequest{
		// Missing header, request_id, end_to_end_id, amount, payer, payee
	}

	// Call the API
	resp, err := client.PixOutConfirm(context.Background(), req)

	// Assertions
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Should return validation error
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Expected validation error response")
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error)
	assert.Equal(t, "INVALID_PARAMETERS", errorResp.ErrorSubcode)
	assert.Contains(t, errorResp.ErrorMessage, "Parameter validation failed")
	assert.Contains(t, errorResp.ErrorDetails, "request_id: is required")
}

// Test_shouldReturnSuccessResponseWhenPixOutConfirmGivenValidRequestAndJDSuccess tests successful API call
func Test_shouldReturnSuccessResponseWhenPixOutConfirmGivenValidRequestAndJDSuccess(t *testing.T) {
	// Setup test server
	testServer := fixture.NewTestServer(t)
	defer testServer.Close()

	// Configure mock client for success response
	mockPixPaymentClient := testServer.GetMockPixPaymentClient()
	mockPixPaymentClient.SetResponse("PixOutConfirm", &fixture.APIResponse{
		Success: map[string]interface{}{
			"idReqSistemaCliente": "test-client-req-123",
			"idReqJdPi":           "jd-req-456",
			"endToEndId":          "E********20241225153000000000123",
			"dtHrReqJdPi":         time.Now().Format(time.RFC3339),
		},
		ShouldErr: false,
	})

	// Create gRPC client
	client := testServer.CreatePixPaymentServiceClient()

	// Create valid request
	req := &pixrequest.PixOutConfirmRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "test-idempotence-123",
		},
		RequestId:           "test-client-req-123",
		RequestTime:         timestamppb.New(time.Now()),
		TransactionType:     wallet.PixTransactionType_PIX_KEY,
		LiquidationPriority: wallet.PixTransactionPriority_NON_PRIORITY_LIQUIDATION,
		PaymentPriority:     wallet.PixTransactionPriorityType_PRIORITY_PAYMENT,
		Purpose:             wallet.PixTransactionPurpose_PURCHASE_OR_TRANSFER,
		AgentModality:       wallet.PixWithdrawalAgentType_COMMERCIAL_ESTABLISHMENT,
		PssIspb:             ********,
		Payer: &wallet.BankAccount{
			Ispb:          ********,
			BranchCode:    "0001",
			AccountNumber: "********9",
			AccountType:   wallet.BankAccountType_CHECKING_ACCOUNT,
		},
		PayerHolder: &wallet.BankAccountHolder{
			HolderType: wallet.AccountHolderType_NATURAL,
			HolderName: "Test Payer",
			DocumentId: "********901",
		},
		Payee: &wallet.BankAccount{
			Ispb:          ********,
			BranchCode:    "0001",
			AccountNumber: "9********",
			AccountType:   wallet.BankAccountType_CHECKING_ACCOUNT,
		},
		PayeeHolder: &wallet.BankAccountHolder{
			HolderType: wallet.AccountHolderType_NATURAL,
			HolderName: "Test Payee",
			DocumentId: "***********",
		},
		Amount:                100.50,
		PixKey:                "<EMAIL>",
		EndToEndId:            "E********20241225153000000000123",
		PayeeReconciliationId: "reconciliation-123",
		ClientInfo:            "Test payment",
	}

	// Call the API
	resp, err := client.PixOutConfirm(context.Background(), req)

	// Assertions
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Should return success response
	successResp := resp.GetResponse()
	require.NotNil(t, successResp, "Expected success response")
	assert.Equal(t, "test-client-req-123", successResp.RequestId)
	assert.Equal(t, "jd-req-456", successResp.ChannelRequestId)
	assert.Equal(t, "E********20241225153000000000123", successResp.EndToEndId)
	assert.NotNil(t, successResp.ChannelRequestTime)
}

// Test_shouldReturnErrorResponseWhenPixOutConfirmGivenValidRequestButJDFailure tests JD API error handling
func Test_shouldReturnErrorResponseWhenPixOutConfirmGivenValidRequestButJDFailure(t *testing.T) {
	// Setup test server
	testServer := fixture.NewTestServer(t)
	defer testServer.Close()

	// Configure mock client for error response
	mockPixPaymentClient := testServer.GetMockPixPaymentClient()
	mockPixPaymentClient.SetResponse("PixOutConfirm", &fixture.APIResponse{
		Error: &fixture.JDAPIError{
			Codigo:       "JDPI300",
			Mensagem:     "Insufficient funds for the transaction",
			IdCorrelacao: "correlation-123",
		},
		ShouldErr: true,
	})

	// Create gRPC client
	client := testServer.CreatePixPaymentServiceClient()

	// Create valid request
	req := &pixrequest.PixOutConfirmRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "test-idempotence-123",
		},
		RequestId:           "test-client-req-123",
		RequestTime:         timestamppb.New(time.Now()),
		TransactionType:     wallet.PixTransactionType_PIX_KEY,
		LiquidationPriority: wallet.PixTransactionPriority_NON_PRIORITY_LIQUIDATION,
		PaymentPriority:     wallet.PixTransactionPriorityType_PRIORITY_PAYMENT,
		Purpose:             wallet.PixTransactionPurpose_PURCHASE_OR_TRANSFER,
		AgentModality:       wallet.PixWithdrawalAgentType_COMMERCIAL_ESTABLISHMENT,
		PssIspb:             ********,
		Payer: &wallet.BankAccount{
			Ispb:          ********,
			BranchCode:    "0001",
			AccountNumber: "********9",
			AccountType:   wallet.BankAccountType_CHECKING_ACCOUNT,
		},
		PayerHolder: &wallet.BankAccountHolder{
			HolderType: wallet.AccountHolderType_NATURAL,
			HolderName: "Test Payer",
			DocumentId: "********901",
		},
		Payee: &wallet.BankAccount{
			Ispb:          ********,
			BranchCode:    "0001",
			AccountNumber: "9********",
			AccountType:   wallet.BankAccountType_CHECKING_ACCOUNT,
		},
		PayeeHolder: &wallet.BankAccountHolder{
			HolderType: wallet.AccountHolderType_NATURAL,
			HolderName: "Test Payee",
			DocumentId: "***********",
		},
		Amount:                100.50,
		PixKey:                "<EMAIL>",
		EndToEndId:            "E********20241225153000000000123",
		PayeeReconciliationId: "reconciliation-123",
		ClientInfo:            "Test payment",
	}

	// Call the API
	resp, err := client.PixOutConfirm(context.Background(), req)

	// Assertions
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Should return error response
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Expected error response")
	assert.Equal(t, pixresponse.ErrorCode_INTERNAL_ERROR, errorResp.Error)
	assert.Equal(t, "INTERNAL_SERVER_ERROR", errorResp.ErrorSubcode)
	assert.Contains(t, errorResp.ErrorMessage, "Internal error occurred")
}
