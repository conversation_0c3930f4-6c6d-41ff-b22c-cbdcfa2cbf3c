package integration

import (
	"context"
	"testing"
	"time"

	"jdpi-gateway/internal/model"
	"jdpi-gateway/test/integration/fixture"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	// External protocol imports
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// Test_shouldReturnValidationErrorWhenGetPixKeyGivenInvalidRequest tests parameter validation failure
func Test_shouldReturnValidationErrorWhenGetPixKeyGivenInvalidRequest(t *testing.T) {
	// Setup test server with default mocks
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create test request with missing PIX key (should cause validation error)
	req := &pixrequest.PixKeyGetRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "147b46bf2862400dafbbc1897766316d",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb:                ********,
		E2EId:               "a6b6ce2e3b42474dba5920d9b9db4b2e",
		RequesterDocumentId: "**************",
		PixKey:              nil, // Missing PIX key should cause validation error
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyGet(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyGet should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for missing PIX key")
	require.Nil(t, resp.GetResponse(), "Should not get success response for missing PIX key")

	// Verify error details
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "INVALID_PARAMETERS", errorResp.ErrorSubcode, "Error subcode should indicate invalid parameters")
	assert.Contains(t, errorResp.ErrorMessage, "Parameter validation failed", "Error message should mention validation failure")

	t.Logf("✅ GetPixKey validation error test completed successfully")
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}

// Test_shouldReturnPixKeyInfoWhenGetPixKeyGivenValidRequestAndJDSuccess tests successful PIX key retrieval
func Test_shouldReturnPixKeyInfoWhenGetPixKeyGivenValidRequestAndJDSuccess(t *testing.T) {
	// Setup test server with custom success response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return success response matching JD API format
	successResponse := &model.QueryPixKeyJDResponse{
		PixKeyType:                   4, // EVP type
		PixKey:                       "8a85633a-3cf9-4187-9717-f87bff3ab33e",
		Ispb:                         ********,
		Branch:                       "1",
		AccountType:                  0, // CHECKING_ACCOUNT
		Account:                      "1980226",
		AccountOpeningDatetime:       "2025-08-14T13:31:04.379Z",
		PersonType:                   1, // LEGAL
		Document:                     **************,
		Name:                         "Gordon CNPJ DEV XLLL",
		TradeName:                    "",
		PixKeyCreationDatetime:       "2025-08-14T13:31:04.544Z",
		PixKeyOwnershipStartDatetime: "2025-08-14T13:31:04.544Z",
		ClaimOpeningDatetime:         "2025-08-14T13:31:04.544Z",
		EndToEndId:                   "a6b6ce2e3b42474dba5920d9b9db4b2e",
		Statistics: model.StatisticsDto{
			LastAntiFraudUpdateDatetime: "2025-08-14T13:31:04.544Z",
			Counters: []model.CounterDto{
				{Type: 1, Aggregated: 0, D3: 0, D30: 0, M6: 0},
			},
		},
	}
	mockPixDictClient.SetResponse("QueryPixKey", &fixture.APIResponse{
		Success:   successResponse,
		ShouldErr: false,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request matching the provided example
	req := &pixrequest.PixKeyGetRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "147b46bf2862400dafbbc1897766316d",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb:                ********,
		E2EId:               "a6b6ce2e3b42474dba5920d9b9db4b2e",
		RequesterDocumentId: "**************",
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EVP,
			KeyValue: "8a85633a-3cf9-4187-9717-f87bff3ab33e",
		},
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyGet(ctx, req)

	// Verify no error occurred
	require.NoError(t, err, "PixKeyGet should not return an error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got a success response
	successResp := resp.GetResponse()
	require.NotNil(t, successResp, "Should get success response")
	require.Nil(t, resp.GetError(), "Should not get error response")

	// Verify PIX key information
	assert.NotNil(t, successResp.PixKey, "Response should contain PIX key information")
	assert.Equal(t, wallet.PixKeyType_EVP, successResp.PixKey.KeyType, "PIX key type should be EVP")
	assert.Equal(t, "8a85633a-3cf9-4187-9717-f87bff3ab33e", successResp.PixKey.KeyValue, "PIX key value should match")

	// Verify bank account information
	assert.NotNil(t, successResp.BankAccount, "Response should contain bank account information")
	assert.Equal(t, int32(********), successResp.BankAccount.Ispb, "ISPB should match")
	assert.Equal(t, "1", successResp.BankAccount.BranchCode, "Branch code should match")
	assert.Equal(t, "1980226", successResp.BankAccount.AccountNumber, "Account number should match")
	assert.Equal(t, wallet.BankAccountType_CHECKING_ACCOUNT, successResp.BankAccount.AccountType, "Account type should be CHECKING_ACCOUNT")
	assert.Equal(t, "ACTIVE", successResp.BankAccount.Status, "Account status should be ACTIVE")

	// Verify account opening datetime
	assert.NotNil(t, successResp.BankAccount.AccountOpeningDatetime, "Account opening datetime should be present")
	expectedAccountOpeningTime, err := time.Parse(time.RFC3339, "2025-08-14T13:31:04.379Z")
	require.NoError(t, err, "Should parse expected account opening time")
	actualAccountOpeningTime := successResp.BankAccount.AccountOpeningDatetime.AsTime()
	assert.Equal(t, expectedAccountOpeningTime, actualAccountOpeningTime, "Account opening datetime should match")

	// Verify bank account holder information
	assert.NotNil(t, successResp.BankAccountHolder, "Response should contain bank account holder information")
	assert.Equal(t, wallet.AccountHolderType_LEGAL, successResp.BankAccountHolder.HolderType, "Holder type should be LEGAL")
	assert.Equal(t, "Gordon CNPJ DEV XLLL", successResp.BankAccountHolder.HolderName, "Holder name should match")
	assert.Equal(t, "**************", successResp.BankAccountHolder.DocumentId, "Document ID should match")

	t.Logf("✅ GetPixKey success test completed successfully")
	t.Logf("   PIX Key Type: %s", successResp.PixKey.KeyType)
	t.Logf("   PIX Key Value: %s", successResp.PixKey.KeyValue)
	t.Logf("   ISPB: %d", successResp.BankAccount.Ispb)
	t.Logf("   Branch: %s", successResp.BankAccount.BranchCode)
	t.Logf("   Account: %s", successResp.BankAccount.AccountNumber)
	t.Logf("   Holder: %s (%s)", successResp.BankAccountHolder.HolderName, successResp.BankAccountHolder.HolderType)
}

// Test_shouldReturnErrorResponseWhenGetPixKeyGivenValidRequestButJDFailure tests JD API failure handling
func Test_shouldReturnErrorResponseWhenGetPixKeyGivenValidRequestButJDFailure(t *testing.T) {
	// Setup test server with custom error response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return JD API error
	jdError := &model.JDAPIError{
		Message:       "PIX key not found",
		OriginalError: "The requested PIX key does not exist in the dictionary",
		StatusCode:    404,
		JDError: &model.JDError{
			Codigo:   "PIX_KEY_NOT_FOUND",
			Mensagem: "PIX key not found in the dictionary",
		},
	}
	mockPixDictClient.SetResponse("QueryPixKey", &fixture.APIResponse{
		Error:     jdError,
		ShouldErr: true,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.PixKeyGetRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "147b46bf2862400dafbbc1897766316d",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb:                ********,
		E2EId:               "a6b6ce2e3b42474dba5920d9b9db4b2e",
		RequesterDocumentId: "**************",
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EVP,
			KeyValue: "nonexistent-key-uuid",
		},
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyGet(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyGet should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for JD API failure")
	require.Nil(t, resp.GetResponse(), "Should not get success response for JD API failure")

	// Verify error details match JD API error
	assert.Equal(t, pixresponse.ErrorCode_INTERNAL_ERROR, errorResp.Error, "Error code should indicate internal error for 404")
	assert.Equal(t, "PIX_KEY_NOT_FOUND", errorResp.ErrorSubcode, "Error subcode should match JD error code")
	assert.Contains(t, errorResp.ErrorMessage, "JD API failed", "Error message should contain JD error message")

	t.Logf("✅ GetPixKey JD API failure test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}
