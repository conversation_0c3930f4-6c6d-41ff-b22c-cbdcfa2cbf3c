package integration

import (
	"context"
	"testing"

	"jdpi-gateway/internal/model"
	"jdpi-gateway/test/integration/fixture"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	// External protocol imports
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// Test_shouldReturnValidationErrorWhenVerifyPixKeyExistenceGivenInvalidRequest tests parameter validation failure
func Test_shouldReturnValidationErrorWhenVerifyPixKeyExistenceGivenInvalidRequest(t *testing.T) {
	// Setup test server with default mocks
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create test request with empty keys (should cause validation error)
	req := &pixrequest.PixKeyIsExistRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "********************************",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: 48756121,
		Keys: []*wallet.PixKey{}, // Empty keys should cause validation error
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyIsExist(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyIsExist should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for empty keys")
	require.Nil(t, resp.GetResponse(), "Should not get success response for empty keys")

	// Verify error details
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "INVALID_PARAMETERS", errorResp.ErrorSubcode, "Error subcode should indicate invalid parameters")
	assert.Contains(t, errorResp.ErrorMessage, "Parameter validation failed", "Error message should mention validation failure")

	t.Logf("✅ VerifyPixKeyExistence validation error test completed successfully")
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}

// Test_shouldReturnExistentPixKeysWhenVerifyPixKeyExistenceGivenValidRequestAndJDSuccess tests successful PIX key verification
func Test_shouldReturnExistentPixKeysWhenVerifyPixKeyExistenceGivenValidRequestAndJDSuccess(t *testing.T) {
	// Setup test server with custom success response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return success response matching JD API format
	successResponse := &model.VerifyPixKeyExistenceJDResponse{
		DictReturnDatetime: "2025-08-15T10:45:23.914Z",
		CorrelationId:      "B202508150745225884875612148D608",
		VerifiedPixKeys: []model.VerifiedPixKeyJDDto{
			{
				PixKey:       "27056201-6a62-4296-9761-b5cf940ac103",
				ExistsInDict: false,
			},
			{
				PixKey:       "<EMAIL>",
				ExistsInDict: true,
			},
			{
				PixKey:       "27056201-6a62-4296-9761-b5cf940ac102",
				ExistsInDict: false,
			},
		},
	}
	mockPixDictClient.SetResponse("VerifyPixKeyExistence", &fixture.APIResponse{
		Success:   successResponse,
		ShouldErr: false,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request matching the provided example
	req := &pixrequest.PixKeyIsExistRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "********************************",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: 48756121,
		Keys: []*wallet.PixKey{
			{
				KeyType:  wallet.PixKeyType_EVP,
				KeyValue: "27056201-6a62-4296-9761-b5cf940ac103",
			},
			{
				KeyType:  wallet.PixKeyType_EMAIL,
				KeyValue: "<EMAIL>",
			},
			{
				KeyType:  wallet.PixKeyType_EVP,
				KeyValue: "27056201-6a62-4296-9761-b5cf940ac102",
			},
		},
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyIsExist(ctx, req)

	// Verify no error occurred
	require.NoError(t, err, "PixKeyIsExist should not return an error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got a success response
	successResp := resp.GetResponse()
	require.NotNil(t, successResp, "Should get success response")
	require.Nil(t, resp.GetError(), "Should not get error response")

	// Verify only existent PIX keys are returned
	assert.NotNil(t, successResp.ExistentPixKey, "Response should contain existent PIX keys")
	assert.Len(t, successResp.ExistentPixKey, 1, "Should return exactly 1 existent PIX key")

	// Verify the existent PIX key details
	existentKey := successResp.ExistentPixKey[0]
	assert.Equal(t, wallet.PixKeyType_EMAIL, existentKey.KeyType, "Existent key type should be EMAIL")
	assert.Equal(t, "<EMAIL>", existentKey.KeyValue, "Existent key value should match")

	t.Logf("✅ VerifyPixKeyExistence success test completed successfully")
	t.Logf("   Total keys checked: %d", len(req.Keys))
	t.Logf("   Existent keys found: %d", len(successResp.ExistentPixKey))
	t.Logf("   Existent key: %s (%s)", existentKey.KeyValue, existentKey.KeyType)
}

// Test_shouldReturnErrorResponseWhenVerifyPixKeyExistenceGivenValidRequestButJDFailure tests JD API failure handling
func Test_shouldReturnErrorResponseWhenVerifyPixKeyExistenceGivenValidRequestButJDFailure(t *testing.T) {
	// Setup test server with custom error response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return JD API error
	jdError := &model.JDAPIError{
		Message:       "Service temporarily unavailable",
		OriginalError: "The PIX dictionary service is temporarily unavailable",
		StatusCode:    503,
		JDError: &model.JDError{
			Codigo:   "SERVICE_UNAVAILABLE",
			Mensagem: "PIX dictionary service is temporarily unavailable",
		},
	}
	mockPixDictClient.SetResponse("VerifyPixKeyExistence", &fixture.APIResponse{
		Error:     jdError,
		ShouldErr: true,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.PixKeyIsExistRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "********************************",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: 48756121,
		Keys: []*wallet.PixKey{
			{
				KeyType:  wallet.PixKeyType_EMAIL,
				KeyValue: "<EMAIL>",
			},
		},
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyIsExist(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyIsExist should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for JD API failure")
	require.Nil(t, resp.GetResponse(), "Should not get success response for JD API failure")

	// Verify error details match JD API error
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_BUSINESS, errorResp.Error, "Error code should indicate channel business error for 503")
	assert.Equal(t, "SERVICE_UNAVAILABLE", errorResp.ErrorSubcode, "Error subcode should match JD error code")
	assert.Contains(t, errorResp.ErrorMessage, "JD API failed", "Error message should contain JD error message")

	t.Logf("✅ VerifyPixKeyExistence JD API failure test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}
