package integration

import (
	"context"
	"testing"

	"jdpi-gateway/internal/model"
	"jdpi-gateway/test/integration/fixture"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	// External protocol imports
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// Test_shouldReturnValidationErrorWhenListPixKeysGivenInvalidRequest tests parameter validation failure
func Test_shouldReturnValidationErrorWhenListPixKeysGivenInvalidRequest(t *testing.T) {
	// Setup test server with default mocks
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create test request with missing bank account (should cause validation error)
	req := &pixrequest.PixKeyListByAccountRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "86a985f9f80f4811bb03376cdfe19dba",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb:              ********,
		BankAccount:       nil, // Missing bank account should cause validation error
		BankAccountHolder: nil, // Missing bank account holder should cause validation error
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyListByAccount(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyListByAccount should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for missing required fields")
	require.Nil(t, resp.GetResponse(), "Should not get success response for missing required fields")

	// Verify error details
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "INVALID_PARAMETERS", errorResp.ErrorSubcode, "Error subcode should indicate invalid parameters")
	assert.Contains(t, errorResp.ErrorMessage, "Parameter validation failed", "Error message should mention validation failure")

	t.Logf("✅ ListPixKeys validation error test completed successfully")
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}

// Test_shouldReturnPixKeysListWhenListPixKeysGivenValidRequestAndJDSuccess tests successful PIX keys listing
func Test_shouldReturnPixKeysListWhenListPixKeysGivenValidRequestAndJDSuccess(t *testing.T) {
	// Setup test server with custom success response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return success response matching JD API format
	successResponse := &model.ListPixKeysJDResponse{
		JDApiReturnDatetime: "2025-08-15T10:07:41.581Z",
		PixKeys: []model.AssociatedPixKeyJDDto{
			{
				PixKeyType:                   4, // EVP
				PixKey:                       "6adbd0af-c88f-430a-938e-e3856e51e451",
				Name:                         "Gordon CNPJ DEV XLLL",
				PixKeyCreationDatetime:       "2025-08-14T14:33:26.399Z",
				PixKeyOwnershipStartDatetime: "2025-08-14T14:33:26.399Z",
			},
			{
				PixKeyType:                   4, // EVP
				PixKey:                       "8a85633a-3cf9-4187-9717-f87bff3ab33e",
				Name:                         "Gordon CNPJ DEV XLLL",
				PixKeyCreationDatetime:       "2025-08-14T13:31:04.544Z",
				PixKeyOwnershipStartDatetime: "2025-08-14T13:31:04.544Z",
			},
			{
				PixKeyType:                   2, // EMAIL
				PixKey:                       "<EMAIL>",
				Name:                         "Gordon CNPJ DEV XLLL",
				PixKeyCreationDatetime:       "2025-08-15T10:06:59.978Z",
				PixKeyOwnershipStartDatetime: "2025-08-15T10:06:59.975Z",
			},
		},
	}
	mockPixDictClient.SetResponse("ListPixKeys", &fixture.APIResponse{
		Success:   successResponse,
		ShouldErr: false,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request matching the provided example
	req := &pixrequest.PixKeyListByAccountRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "86a985f9f80f4811bb03376cdfe19dba",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		BankAccount: &wallet.BankAccount{
			Ispb:                   ********,
			BranchCode:             "0001",
			AccountNumber:          "*********",
			AccountType:            wallet.BankAccountType_CHECKING_ACCOUNT,
			Status:                 "ACTIVE",
			AccountOpeningDatetime: timestamppb.Now(),
		},
		BankAccountHolder: &wallet.BankAccountHolder{
			HolderType:     wallet.AccountHolderType_LEGAL,
			HolderName:     "Gordon CNPJ DEV XLLL",
			HolderNickname: "",
			DocumentId:     "**************",
		},
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyListByAccount(ctx, req)

	// Verify no error occurred
	require.NoError(t, err, "PixKeyListByAccount should not return an error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got a success response
	successResp := resp.GetResponse()
	require.NotNil(t, successResp, "Should get success response")
	require.Nil(t, resp.GetError(), "Should not get error response")

	// Verify PIX keys list
	assert.NotNil(t, successResp.PixKey, "Response should contain PIX keys list")
	assert.Len(t, successResp.PixKey, 3, "Should return 3 PIX keys")

	// Verify first PIX key (EVP)
	firstKey := successResp.PixKey[0]
	assert.Equal(t, wallet.PixKeyType_EVP, firstKey.KeyType, "First key type should be EVP")
	assert.Equal(t, "6adbd0af-c88f-430a-938e-e3856e51e451", firstKey.KeyValue, "First key value should match")

	// Verify second PIX key (EVP)
	secondKey := successResp.PixKey[1]
	assert.Equal(t, wallet.PixKeyType_EVP, secondKey.KeyType, "Second key type should be EVP")
	assert.Equal(t, "8a85633a-3cf9-4187-9717-f87bff3ab33e", secondKey.KeyValue, "Second key value should match")

	// Verify third PIX key (EMAIL)
	thirdKey := successResp.PixKey[2]
	assert.Equal(t, wallet.PixKeyType_EMAIL, thirdKey.KeyType, "Third key type should be EMAIL")
	assert.Equal(t, "<EMAIL>", thirdKey.KeyValue, "Third key value should match")

	t.Logf("✅ ListPixKeys success test completed successfully")
	t.Logf("   Total PIX keys found: %d", len(successResp.PixKey))
	for i, key := range successResp.PixKey {
		t.Logf("   Key %d: %s (%s)", i+1, key.KeyValue, key.KeyType)
	}
}

// Test_shouldReturnErrorResponseWhenListPixKeysGivenValidRequestButJDFailure tests JD API failure handling
func Test_shouldReturnErrorResponseWhenListPixKeysGivenValidRequestButJDFailure(t *testing.T) {
	// Setup test server with custom error response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return JD API error
	jdError := &model.JDAPIError{
		Message:       "Account not found",
		OriginalError: "The specified bank account does not exist",
		StatusCode:    404,
		JDError: &model.JDError{
			Codigo:   "ACCOUNT_NOT_FOUND",
			Mensagem: "Bank account not found in the system",
		},
	}
	mockPixDictClient.SetResponse("ListPixKeys", &fixture.APIResponse{
		Error:     jdError,
		ShouldErr: true,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.PixKeyListByAccountRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "86a985f9f80f4811bb03376cdfe19dba",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: ********,
		BankAccount: &wallet.BankAccount{
			Ispb:                   ********,
			BranchCode:             "9999",      // Non-existent branch
			AccountNumber:          "*********", // Non-existent account
			AccountType:            wallet.BankAccountType_CHECKING_ACCOUNT,
			Status:                 "ACTIVE",
			AccountOpeningDatetime: timestamppb.Now(),
		},
		BankAccountHolder: &wallet.BankAccountHolder{
			HolderType:     wallet.AccountHolderType_LEGAL,
			HolderName:     "Non Existent User",
			HolderNickname: "",
			DocumentId:     "**************",
		},
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyListByAccount(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyListByAccount should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for JD API failure")
	require.Nil(t, resp.GetResponse(), "Should not get success response for JD API failure")

	// Verify error details match JD API error
	assert.Equal(t, pixresponse.ErrorCode_INTERNAL_ERROR, errorResp.Error, "Error code should indicate internal error for 404")
	assert.Equal(t, "ACCOUNT_NOT_FOUND", errorResp.ErrorSubcode, "Error subcode should match JD error code")
	assert.Contains(t, errorResp.ErrorMessage, "JD API failed", "Error message should contain JD error message")

	t.Logf("✅ ListPixKeys JD API failure test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}
