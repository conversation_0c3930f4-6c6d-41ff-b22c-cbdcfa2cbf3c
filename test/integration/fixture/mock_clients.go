package fixture

import (
	"context"
	"fmt"
	"time"

	"jdpi-gateway/internal/model"

	"go.uber.org/zap"
)

// PixDictClientInterface defines the interface for PIX dict client operations
type PixDictClientInterface interface {
	CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest, idempotenceID string) (*model.CreatePixKeyJDResponse, error)
	QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest, idempotenceID string) (*model.QueryPixKeyJDResponse, error)
	VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest, idempotenceID string) (*model.VerifyPixKeyExistenceJDResponse, error)
	UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest, idempotenceID string) (*model.UpdatePixKeyJDResponse, error)
	DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest, idempotenceID string) (*model.DeletePixKeyJDResponse, error)
	ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest, idempotenceID string) (*model.ListPixKeysJDResponse, error)
	NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest, idempotenceID string) (*model.NotifyAccountClosureJDResponse, error)
	NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest, idempotenceID string) (*model.NotifyOwnershipLinkClosureJDResponse, error)
}

// PixQRCodeClientInterface defines the interface for PIX QRCode client operations
type PixQRCodeClientInterface interface {
	CreateStaticQRCode(ctx context.Context, req *model.QRCodeStaticCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeStaticCreateJDResponse, error)
	CreateDynamicQRCode(ctx context.Context, req *model.QRCodeDynamicCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicCreateJDResponse, error)
	DecodeQRCodeByPayload(ctx context.Context, req *model.QRCodeGetByPayloadJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeGetByPayloadJDResponse, error)
	DecodeDynamicQRCodeByUrl(ctx context.Context, req *model.QRCodeDynamicDecodeByUrlJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicDecodeByUrlJDResponse, error)
}

// PixPaymentClientInterface defines the interface for PIX Payment client operations
type PixPaymentClientInterface interface {
	PixOutConfirm(ctx context.Context, req *model.PixOutConfirmJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixOutConfirmJDResponse, error)
	PixTransactionGet(ctx context.Context, req *model.PixTransactionGetJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixTransactionGetJDResponse, error)
}

// AuthServiceInterface defines the interface for auth service operations
type AuthServiceInterface interface {
	GetAccessToken(ctx context.Context, requestID string) (*model.AccessToken, error)
	StartTokenRefreshRoutine(ctx context.Context)
	StopTokenRefreshRoutine()
}

// APIResponse represents a generic API response that can be success or error
type APIResponse struct {
	Success   interface{} // Success response data
	Error     error       // Error response
	ShouldErr bool        // Whether this response should return an error
}

// MockPixDictClient provides a flexible mock implementation for PIX dict operations
type MockPixDictClient struct {
	logger    *zap.Logger
	responses map[string]*APIResponse // Map of API method name to response
}

// NewMockPixDictClient creates a new mock PIX dict client
func NewMockPixDictClient(logger *zap.Logger) *MockPixDictClient {
	return &MockPixDictClient{
		logger:    logger,
		responses: make(map[string]*APIResponse),
	}
}

// SetResponse configures the response for a specific API method
func (c *MockPixDictClient) SetResponse(apiMethod string, response *APIResponse) {
	c.responses[apiMethod] = response
}

// CreatePixKey mock implementation
func (c *MockPixDictClient) CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest, idempotenceID string) (*model.CreatePixKeyJDResponse, error) {
	c.logger.Info("Mock: CreatePixKey called", zap.String("pix_key", req.PixKey), zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["CreatePixKey"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.CreatePixKeyJDResponse), nil
		}
	}

	// Default response
	return &model.CreatePixKeyJDResponse{
		PixKey:                       req.PixKey,
		PixKeyCreationDatetime:       "2025-08-15T10:06:59.978Z",
		PixKeyOwnershipStartDatetime: "2025-08-15T10:06:59.975Z",
		ClaimOpeningDatetime:         "2025-08-15T10:06:59.975Z",
	}, nil
}

// QueryPixKey mock implementation
func (c *MockPixDictClient) QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest, idempotenceID string) (*model.QueryPixKeyJDResponse, error) {
	c.logger.Info("Mock: QueryPixKey called", zap.String("pix_key", req.PixKey), zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["QueryPixKey"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.QueryPixKeyJDResponse), nil
		}
	}

	// Default response
	return &model.QueryPixKeyJDResponse{
		PixKeyType:                   2, // EMAIL
		PixKey:                       req.PixKey,
		Ispb:                         ********,
		Branch:                       "1",
		AccountType:                  0,
		Account:                      "1980226",
		AccountOpeningDatetime:       "2025-08-14T13:31:04.379Z",
		PersonType:                   1,
		Document:                     **************,
		Name:                         "Mock User",
		TradeName:                    "",
		PixKeyCreationDatetime:       "2025-08-14T13:31:04.544Z",
		PixKeyOwnershipStartDatetime: "2025-08-14T13:31:04.544Z",
		ClaimOpeningDatetime:         "2025-08-14T13:31:04.544Z",
		EndToEndId:                   req.EndToEndId,
		Statistics: model.StatisticsDto{
			LastAntiFraudUpdateDatetime: "2025-08-14T13:31:04.544Z",
			Counters: []model.CounterDto{
				{Type: 1, Aggregated: 0, D3: 0, D30: 0, M6: 0},
			},
		},
	}, nil
}

// VerifyPixKeyExistence mock implementation
func (c *MockPixDictClient) VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest, idempotenceID string) (*model.VerifyPixKeyExistenceJDResponse, error) {
	c.logger.Info("Mock: VerifyPixKeyExistence called", zap.Int("keys_count", len(req.PixKeysToVerify)), zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["VerifyPixKeyExistence"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.VerifyPixKeyExistenceJDResponse), nil
		}
	}

	// Default response - assume all keys exist
	verifiedPixKeys := make([]model.VerifiedPixKeyJDDto, len(req.PixKeysToVerify))
	for i, pixKey := range req.PixKeysToVerify {
		verifiedPixKeys[i] = model.VerifiedPixKeyJDDto{
			PixKey:       pixKey.PixKey,
			ExistsInDict: true,
		}
	}

	return &model.VerifyPixKeyExistenceJDResponse{
		DictReturnDatetime: "2025-08-15T10:45:23.914Z",
		CorrelationId:      "mock-correlation-id",
		VerifiedPixKeys:    verifiedPixKeys,
	}, nil
}

// UpdatePixKey mock implementation
func (c *MockPixDictClient) UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest, idempotenceID string) (*model.UpdatePixKeyJDResponse, error) {
	c.logger.Info("Mock: UpdatePixKey called", zap.String("pix_key", req.PixKey), zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["UpdatePixKey"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.UpdatePixKeyJDResponse), nil
		}
	}

	// Default response
	return &model.UpdatePixKeyJDResponse{
		PixKey:                       req.PixKey,
		PixKeyCreationDatetime:       "2025-08-15T10:06:59.978Z",
		PixKeyOwnershipStartDatetime: "2025-08-15T10:06:59.975Z",
		ClaimOpeningDatetime:         "2025-08-15T10:06:59.975Z",
	}, nil
}

// DeletePixKey mock implementation
func (c *MockPixDictClient) DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest, idempotenceID string) (*model.DeletePixKeyJDResponse, error) {
	c.logger.Info("Mock: DeletePixKey called", zap.String("pix_key", req.PixKey), zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["DeletePixKey"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.DeletePixKeyJDResponse), nil
		}
	}

	// Default response
	return &model.DeletePixKeyJDResponse{
		PixKey: req.PixKey,
	}, nil
}

// ListPixKeys mock implementation
func (c *MockPixDictClient) ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest, idempotenceID string) (*model.ListPixKeysJDResponse, error) {
	c.logger.Info("Mock: ListPixKeys called", zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["ListPixKeys"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.ListPixKeysJDResponse), nil
		}
	}

	// Default response
	return &model.ListPixKeysJDResponse{
		JDApiReturnDatetime: "2025-08-15T10:45:23.914Z",
		PixKeys: []model.AssociatedPixKeyJDDto{
			{
				PixKeyType:                   2, // EMAIL
				PixKey:                       "<EMAIL>",
				Name:                         "Mock User",
				PixKeyCreationDatetime:       "2025-08-15T10:06:59.978Z",
				PixKeyOwnershipStartDatetime: "2025-08-15T10:06:59.975Z",
			},
		},
	}, nil
}

// NotifyAccountClosure mock implementation
func (c *MockPixDictClient) NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest, idempotenceID string) (*model.NotifyAccountClosureJDResponse, error) {
	c.logger.Info("Mock: NotifyAccountClosure called", zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["NotifyAccountClosure"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.NotifyAccountClosureJDResponse), nil
		}
	}

	// Default response
	return &model.NotifyAccountClosureJDResponse{}, nil
}

// NotifyOwnershipLinkClosure mock implementation
func (c *MockPixDictClient) NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest, idempotenceID string) (*model.NotifyOwnershipLinkClosureJDResponse, error) {
	c.logger.Info("Mock: NotifyOwnershipLinkClosure called", zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["NotifyOwnershipLinkClosure"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.NotifyOwnershipLinkClosureJDResponse), nil
		}
	}

	// Default response
	return &model.NotifyOwnershipLinkClosureJDResponse{}, nil
}

// MockAuthService provides a mock implementation for auth service operations
type MockAuthService struct {
	logger *zap.Logger
}

// NewMockAuthService creates a new mock auth service
func NewMockAuthService(logger *zap.Logger) *MockAuthService {
	return &MockAuthService{
		logger: logger,
	}
}

// GetAccessToken mock implementation
func (s *MockAuthService) GetAccessToken(ctx context.Context, requestID string) (*model.AccessToken, error) {
	s.logger.Info("Mock: GetAccessToken called", zap.String("request_id", requestID))

	return &model.AccessToken{
		AccessToken: "mock-access-token",
		ExpiresIn:   3600,
	}, nil
}

// StartTokenRefreshRoutine mock implementation
func (s *MockAuthService) StartTokenRefreshRoutine(ctx context.Context) {
	s.logger.Info("Mock: StartTokenRefreshRoutine called")
	// No-op for testing
}

// StopTokenRefreshRoutine mock implementation
func (s *MockAuthService) StopTokenRefreshRoutine() {
	s.logger.Info("Mock: StopTokenRefreshRoutine called")
	// No-op for testing
}

// MockPixQRCodeClient provides a flexible mock implementation for PIX QRCode operations
type MockPixQRCodeClient struct {
	logger    *zap.Logger
	responses map[string]*APIResponse // Map of API method name to response
}

// NewMockPixQRCodeClient creates a new mock PIX QRCode client
func NewMockPixQRCodeClient(logger *zap.Logger) *MockPixQRCodeClient {
	return &MockPixQRCodeClient{
		logger:    logger,
		responses: make(map[string]*APIResponse),
	}
}

// SetResponse configures the response for a specific API method
func (c *MockPixQRCodeClient) SetResponse(apiMethod string, response *APIResponse) {
	c.responses[apiMethod] = response
}

// QRCodeStaticCreate mock implementation
func (c *MockPixQRCodeClient) CreateStaticQRCode(ctx context.Context, req *model.QRCodeStaticCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeStaticCreateJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	c.logger.Info("Mock: CreateStaticQRCode called",
		zap.String("pix_key", req.PixKey),
		zap.String("idempotence_id", idempotenceID),
		zap.Any("headers", headers))

	if resp, exists := c.responses["CreateStaticQRCode"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.QRCodeStaticCreateJDResponse), nil
		}
	}

	// Default response
	return &model.QRCodeStaticCreateJDResponse{
		QRCodeImageBase64:   "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
		QRCodePayloadBase64: "MDAwMjAxMjY1ODAwMTRCUi5HT1YuQkNCLlBJWA==",
	}, nil
}

// QRCodeDynamicCreate mock implementation
func (c *MockPixQRCodeClient) CreateDynamicQRCode(ctx context.Context, req *model.QRCodeDynamicCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicCreateJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	c.logger.Info("Mock: CreateDynamicQRCode called",
		zap.String("pix_key", req.PixKey),
		zap.String("idempotence_id", idempotenceID),
		zap.Any("headers", headers))

	if resp, exists := c.responses["CreateDynamicQRCode"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.QRCodeDynamicCreateJDResponse), nil
		}
	}

	// Default response
	return &model.QRCodeDynamicCreateJDResponse{
		DocumentID:          "doc-12345",
		QRCodeImageBase64:   "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
		QRCodePayloadBase64: "MDAwMjAxMjY1ODAwMTRCUi5HT1YuQkNCLlBJWA==",
		PayloadJWS:          "eyJhbGciOiJSUzI1NiJ9.eyJpc3MiOiJKRCJ9.signature",
		Revision:            1,
	}, nil
}

// QRCodeGetByPayload mock implementation
func (c *MockPixQRCodeClient) DecodeQRCodeByPayload(ctx context.Context, req *model.QRCodeGetByPayloadJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeGetByPayloadJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	c.logger.Info("Mock: DecodeQRCodeByPayload called",
		zap.String("qrcode_payload", req.QRCodePayload),
		zap.String("idempotence_id", idempotenceID),
		zap.Any("headers", headers))

	if resp, exists := c.responses["DecodeQRCodeByPayload"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.QRCodeGetByPayloadJDResponse), nil
		}
	}

	// Default response
	return &model.QRCodeGetByPayloadJDResponse{
		EndToEndID: "E********90********90********9012",
		QRCodeType: 11, // STATIC
		StaticQRCodeData: &model.StaticQRCodeDataJD{
			Amount:                   100.50,
			AdditionalData:           "Test QR Code",
			Ispb:                     ********,
			PixKey:                   "<EMAIL>",
			CategoryCode:             "0014",
			ReceiverName:             "Test User",
			ReceiverPersonType:       1,
			ReceiverCPFCNPJ:          ********901,
			City:                     "São Paulo",
			ReceiverReconciliationID: "test-tx-id",
		},
	}, nil
}

// QRCodeDynamicDecodeByUrl mock implementation
func (c *MockPixQRCodeClient) DecodeDynamicQRCodeByUrl(ctx context.Context, req *model.QRCodeDynamicDecodeByUrlJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicDecodeByUrlJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	c.logger.Info("Mock: DecodeDynamicQRCodeByUrl called",
		zap.String("url_payload_json", req.URLPayloadJSON),
		zap.String("idempotence_id", idempotenceID),
		zap.Any("headers", headers))

	if resp, exists := c.responses["DecodeDynamicQRCodeByUrl"]; exists {
		if resp.ShouldErr {
			return nil, resp.Error
		}
		if resp.Success != nil {
			return resp.Success.(*model.QRCodeDynamicDecodeByUrlJDResponse), nil
		}
	}

	// Default response
	return &model.QRCodeDynamicDecodeByUrlJDResponse{
		EndToEndID: "E********90********90********9012",
		QRCodeType: 12, // DYNAMIC_INSTANT_PAYMENT
		DynamicQRCodeData: &model.DynamicQRCodeDataJD{
			Revision:                 1,
			Ispb:                     ********,
			PixKey:                   "<EMAIL>",
			ReceiverReconciliationID: "test-tx-id",
			CategoryCode:             "0014",
			ReceiverName:             "Test User",
			ReceiverPersonType:       1,
			ReceiverCPFCNPJ:          ********901,
			City:                     "São Paulo",
			PostalCode:               "01234567",
			OriginalAmount:           100.50,
			QRExpiration:             3600,
			Reusable:                 false,
			Status:                   1,
		},
	}, nil
}

// MockPixPaymentClient provides a flexible mock implementation for PIX payment operations
type MockPixPaymentClient struct {
	logger    *zap.Logger
	responses map[string]*APIResponse // Map of API method name to response
}

// NewMockPixPaymentClient creates a new mock PIX payment client
func NewMockPixPaymentClient(logger *zap.Logger) *MockPixPaymentClient {
	return &MockPixPaymentClient{
		logger:    logger,
		responses: make(map[string]*APIResponse),
	}
}

// SetResponse configures the response for a specific API method
func (c *MockPixPaymentClient) SetResponse(apiMethod string, response *APIResponse) {
	c.responses[apiMethod] = response
}

// PixOutConfirm mock implementation
func (c *MockPixPaymentClient) PixOutConfirm(ctx context.Context, req *model.PixOutConfirmJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixOutConfirmJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	c.logger.Info("Mock: PixOutConfirm called",
		zap.String("client_request_id", req.ClientRequestID),
		zap.String("end_to_end_id", req.EndToEndID),
		zap.Float64("amount", req.Amount),
		zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["PixOutConfirm"]; exists {
		if resp.ShouldErr {
			if jdErr, ok := resp.Error.(*JDAPIError); ok {
				return nil, fmt.Errorf("JD API error: %s - %s", jdErr.Codigo, jdErr.Mensagem)
			}
			return nil, resp.Error
		}
		if resp.Success != nil {
			// Convert map to struct
			successData := resp.Success.(map[string]interface{})
			return &model.PixOutConfirmJDResponse{
				ClientRequestID: successData["idReqSistemaCliente"].(string),
				JDRequestID:     successData["idReqJdPi"].(string),
				EndToEndID:      successData["endToEndId"].(string),
				JDRequestTime:   parseTimeFromString(successData["dtHrReqJdPi"].(string)),
			}, nil
		}
	}

	// Default response
	return &model.PixOutConfirmJDResponse{
		ClientRequestID: req.ClientRequestID,
		JDRequestID:     "mock-jd-req-12345",
		EndToEndID:      req.EndToEndID,
		JDRequestTime:   time.Now(),
	}, nil
}

// PixTransactionGet mock implementation
func (c *MockPixPaymentClient) PixTransactionGet(ctx context.Context, req *model.PixTransactionGetJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixTransactionGetJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	c.logger.Info("Mock: PixTransactionGet called",
		zap.String("end_to_end_id", req.JDRequestID),
		zap.String("idempotence_id", idempotenceID))

	if resp, exists := c.responses["PixTransactionGet"]; exists {
		if resp.ShouldErr {
			if jdErr, ok := resp.Error.(*JDAPIError); ok {
				return nil, fmt.Errorf("JD API error: %s - %s", jdErr.Codigo, jdErr.Mensagem)
			}
			return nil, resp.Error
		}
		if resp.Success != nil {
			// Convert map to struct
			successData := resp.Success.(map[string]interface{})
			payerData := successData["pagador"].(map[string]interface{})
			payeeData := successData["recebedor"].(map[string]interface{})

			return &model.PixTransactionGetJDResponse{
				JDRequestID:         successData["idReqJdPiConsultada"].(string),
				JDRequestTime:       parseTimeFromString(successData["dtHrReqJdPi"].(string)),
				StatusTime:          parseTimeFromString(successData["dtHrSituacao"].(string)),
				IntermediateStatus:  int(successData["stJdPiProc"].(float64)),
				FinalStatus:         int(successData["stJdPi"].(float64)),
				EndToEndID:          successData["endToEndId"].(string),
				ExecutionTime:       parseTimeFromString(successData["dtHrEfetivacao"].(string)),
				InitiationType:      int(successData["tpIniciacao"].(float64)),
				LiquidationPriority: int(successData["prioridadePagamento"].(float64)),
				PaymentPriority:     int(successData["tpPrioridadePagamento"].(float64)),
				Purpose:             int(successData["finalidade"].(float64)),
				InitiatorCNPJ:       int64(successData["cnpjIniciadorPagamento"].(float64)),
				Payer: model.PixParticipantJD{
					Ispb:          int(payerData["ispb"].(float64)),
					PersonType:    int(payerData["tpPessoa"].(float64)),
					CpfCnpj:       int64(payerData["cpfCnpj"].(float64)),
					Name:          payerData["nome"].(string),
					AgencyNumber:  payerData["nrAgencia"].(string),
					AccountType:   int(payerData["tpConta"].(float64)),
					AccountNumber: payerData["nrConta"].(string),
				},
				Payee: model.PixParticipantJD{
					Ispb:          int(payeeData["ispb"].(float64)),
					PersonType:    int(payeeData["tpPessoa"].(float64)),
					CpfCnpj:       int64(payeeData["cpfCnpj"].(float64)),
					Name:          payeeData["nome"].(string),
					AgencyNumber:  payeeData["nrAgencia"].(string),
					AccountType:   int(payeeData["tpConta"].(float64)),
					AccountNumber: payeeData["nrConta"].(string),
				},
				Amount:                successData["valor"].(float64),
				AmountDetails:         []model.PixAmountDetailJD{},
				PixKey:                stringPtrFromInterface(successData["chave"]),
				PayeeReconciliationID: stringPtrFromInterface(successData["idConciliacaoRecebedor"]),
				ClientInfo:            stringPtrFromInterface(successData["infEntreClientes"]),
			}, nil
		}
	}

	// Default response
	now := time.Now()
	return &model.PixTransactionGetJDResponse{
		JDRequestID:         "mock-jd-req-12345",
		JDRequestTime:       now.Add(-5 * time.Minute),
		StatusTime:          now,
		IntermediateStatus:  0, // SUCCESS
		FinalStatus:         9, // TRANSACTION_SUCCESS
		EndToEndID:          req.JDRequestID,
		ExecutionTime:       now.Add(-2 * time.Minute),
		InitiationType:      1, // PIX_KEY
		LiquidationPriority: 1, // NON_PRIORITY_LIQUIDATION
		PaymentPriority:     0, // PRIORITY_PAYMENT
		Purpose:             0, // PURCHASE_OR_TRANSFER
		InitiatorCNPJ:       **************,
		Payer: model.PixParticipantJD{
			Ispb:          ********,
			PersonType:    0, // NATURAL
			CpfCnpj:       ********901,
			Name:          "Mock Payer",
			AgencyNumber:  "0001",
			AccountType:   0, // CHECKING_ACCOUNT
			AccountNumber: "********9",
		},
		Payee: model.PixParticipantJD{
			Ispb:          ********,
			PersonType:    0, // NATURAL
			CpfCnpj:       9********00,
			Name:          "Mock Payee",
			AgencyNumber:  "0001",
			AccountType:   0, // CHECKING_ACCOUNT
			AccountNumber: "9********",
		},
		Amount:                100.50,
		AmountDetails:         []model.PixAmountDetailJD{},
		PixKey:                stringPtr("<EMAIL>"),
		PayeeReconciliationID: stringPtr("mock-reconciliation-id"),
		ClientInfo:            stringPtr("Mock client info"),
	}, nil
}

// MockClients holds all mock clients for unified testing
type MockClients struct {
	PixDictClient    *MockPixDictClient
	PixQRCodeClient  *MockPixQRCodeClient
	PixPaymentClient *MockPixPaymentClient
	AuthService      *MockAuthService
}

// NewMockClients creates a new set of mock clients
func NewMockClients(logger *zap.Logger) *MockClients {
	return &MockClients{
		PixDictClient:    NewMockPixDictClient(logger),
		PixQRCodeClient:  NewMockPixQRCodeClient(logger),
		PixPaymentClient: NewMockPixPaymentClient(logger),
		AuthService:      NewMockAuthService(logger),
	}
}

// JDAPIError represents a JD API error for testing
type JDAPIError struct {
	Codigo       string `json:"codigo"`
	Mensagem     string `json:"mensagem"`
	IdCorrelacao string `json:"idCorrelacao"`
}

// Error implements the error interface
func (e *JDAPIError) Error() string {
	return fmt.Sprintf("JD API error: %s - %s", e.Codigo, e.Mensagem)
}

// Helper functions for mock implementations

// stringPtrFromInterface safely converts interface{} to *string
func stringPtrFromInterface(v interface{}) *string {
	if v == nil {
		return nil
	}
	if str, ok := v.(string); ok {
		return &str
	}
	return nil
}

// parseTimeFromString parses time from string, returns current time if parsing fails
func parseTimeFromString(timeStr string) time.Time {
	if parsed, err := time.Parse(time.RFC3339, timeStr); err == nil {
		return parsed
	}
	return time.Now()
}

// stringPtr creates a string pointer
func stringPtr(s string) *string {
	return &s
}
