// Package fixture provides common test infrastructure for integration tests
package fixture

import (
	"context"
	"net"
	"testing"

	"jdpi-gateway/internal/handler"
	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/service"

	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/test/bufconn"

	// External protocol imports
	pixservice "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix"
)

const bufSize = 1024 * 1024

var lis *bufconn.Listener

// PixDictTestServer represents a test gRPC server setup
type PixDictTestServer struct {
	Client  pixservice.PixDictServiceClient
	Cleanup func()
}

// QRCodeTestServer represents a test gRPC server setup for QRCode services
type QRCodeTestServer struct {
	Client  pixservice.PixQRCodeServiceClient
	Cleanup func()
}

// PaymentTestServer represents a test gRPC server setup for Payment services
type PaymentTestServer struct {
	Client  pixservice.PixTransactionServiceClient
	Cleanup func()
}

// TestServer represents a unified test gRPC server setup for all services
type TestServer struct {
	PixDictClient    pixservice.PixDictServiceClient
	PixQRCodeClient  pixservice.PixQRCodeServiceClient
	PixPaymentClient pixservice.PixTransactionServiceClient
	mockClients      *MockClients
	Cleanup          func()
}

// SetupPixDictTestServer creates a gRPC test server with the provided mock clients
func SetupPixDictTestServer(t *testing.T, mockPixDictClient PixDictClientInterface, mockAuthService AuthServiceInterface) *PixDictTestServer {
	logger := zap.NewNop()

	// Create service layer with mocks
	pixDictService := service.NewPixDictService(mockPixDictClient, mockAuthService, logger)

	// Create handler
	pixDictHandler := handler.NewPixDictHandler(pixDictService, logger)

	// Create buffer connection for testing
	lis = bufconn.Listen(bufSize)

	// Create gRPC server with interceptors
	opts := []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(
			interceptor.TraceIDInterceptor(logger),
		),
	}
	s := grpc.NewServer(opts...)

	// Register service
	pixservice.RegisterPixDictServiceServer(s, pixDictHandler)

	// Start server in goroutine
	go func() {
		if err := s.Serve(lis); err != nil {
			t.Logf("Server exited with error: %v", err)
		}
	}()

	// Create client connection
	conn, err := grpc.DialContext(context.Background(), "bufnet",
		grpc.WithContextDialer(bufDialer),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	require.NoError(t, err)

	client := pixservice.NewPixDictServiceClient(conn)

	// Return test server with cleanup function
	return &PixDictTestServer{
		Client: client,
		Cleanup: func() {
			conn.Close()
			s.Stop()
			lis.Close()
		},
	}
}

// SetupQRCodeTestServer creates a gRPC test server for QRCode services with the provided mock clients
func SetupQRCodeTestServer(t *testing.T, mockPixQRCodeClient PixQRCodeClientInterface, mockAuthService AuthServiceInterface) *QRCodeTestServer {
	logger := zap.NewNop()

	// Create service layer with mocks
	pixQRCodeService := service.NewJDPixQRCodeService(mockPixQRCodeClient, mockAuthService, logger)

	// Create handler
	pixQRCodeHandler := handler.NewPixQRCodeHandler(pixQRCodeService, logger)

	// Create buffer connection for testing
	lis = bufconn.Listen(bufSize)

	// Create gRPC server with interceptors
	opts := []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(
			interceptor.TraceIDInterceptor(logger),
		),
	}
	s := grpc.NewServer(opts...)

	// Register service
	pixservice.RegisterPixQRCodeServiceServer(s, pixQRCodeHandler)

	// Start server in goroutine
	go func() {
		if err := s.Serve(lis); err != nil {
			t.Logf("Server exited with error: %v", err)
		}
	}()

	// Create client connection
	conn, err := grpc.DialContext(context.Background(), "bufnet",
		grpc.WithContextDialer(bufDialer),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	require.NoError(t, err)

	client := pixservice.NewPixQRCodeServiceClient(conn)

	// Return test server with cleanup function
	return &QRCodeTestServer{
		Client: client,
		Cleanup: func() {
			conn.Close()
			s.Stop()
			lis.Close()
		},
	}
}

func bufDialer(context.Context, string) (net.Conn, error) {
	return lis.Dial()
}

// NewTestServer creates a unified test server with all services and mock clients
func NewTestServer(t *testing.T) *TestServer {
	logger := zap.NewNop()

	// Create mock clients
	mockClients := NewMockClients(logger)

	// Create service layer with mocks
	pixDictService := service.NewPixDictService(mockClients.PixDictClient, mockClients.AuthService, logger)
	pixQRCodeService := service.NewJDPixQRCodeService(mockClients.PixQRCodeClient, mockClients.AuthService, logger)
	pixPaymentService := service.NewJDPixPaymentService(mockClients.PixPaymentClient, mockClients.AuthService, logger)

	// Create handlers
	pixDictHandler := handler.NewPixDictHandler(pixDictService, logger)
	pixQRCodeHandler := handler.NewPixQRCodeHandler(pixQRCodeService, logger)
	pixPaymentHandler := handler.NewPixPaymentHandler(pixPaymentService, logger)

	// Create buffer connection for testing
	lis = bufconn.Listen(bufSize)

	// Create gRPC server with interceptors
	opts := []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(
			interceptor.TraceIDInterceptor(logger),
		),
	}
	s := grpc.NewServer(opts...)

	// Register all services
	pixservice.RegisterPixDictServiceServer(s, pixDictHandler)
	pixservice.RegisterPixQRCodeServiceServer(s, pixQRCodeHandler)
	pixservice.RegisterPixTransactionServiceServer(s, pixPaymentHandler)

	// Start server in goroutine
	go func() {
		if err := s.Serve(lis); err != nil {
			t.Logf("Server exited with error: %v", err)
		}
	}()

	// Create client connection
	conn, err := grpc.DialContext(context.Background(), "bufnet",
		grpc.WithContextDialer(bufDialer),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	require.NoError(t, err)

	// Create all service clients
	pixDictClient := pixservice.NewPixDictServiceClient(conn)
	pixQRCodeClient := pixservice.NewPixQRCodeServiceClient(conn)
	pixPaymentClient := pixservice.NewPixTransactionServiceClient(conn)

	// Return unified test server with cleanup function
	return &TestServer{
		PixDictClient:    pixDictClient,
		PixQRCodeClient:  pixQRCodeClient,
		PixPaymentClient: pixPaymentClient,
		mockClients:      mockClients,
		Cleanup: func() {
			conn.Close()
			s.Stop()
			lis.Close()
		},
	}
}

// CreatePixPaymentServiceClient returns the PixPayment service client
func (ts *TestServer) CreatePixPaymentServiceClient() pixservice.PixTransactionServiceClient {
	return ts.PixPaymentClient
}

// GetMockPixPaymentClient returns the mock PixPayment client for configuration
func (ts *TestServer) GetMockPixPaymentClient() *MockPixPaymentClient {
	return ts.mockClients.PixPaymentClient
}

// Close closes the test server
func (ts *TestServer) Close() {
	ts.Cleanup()
}
