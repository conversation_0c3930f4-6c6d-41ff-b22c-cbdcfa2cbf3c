package integration

import (
	"context"
	"testing"
	"time"

	"jdpi-gateway/test/integration/fixture"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// Test_shouldReturnValidationErrorWhenPixTransactionGetGivenInvalidRequest tests parameter validation failure
func Test_shouldReturnValidationErrorWhenPixTransactionGetGivenInvalidRequest(t *testing.T) {
	// Setup test server
	testServer := fixture.NewTestServer(t)
	defer testServer.Close()

	// Create gRPC client
	client := testServer.CreatePixPaymentServiceClient()

	// Test case: Missing required fields
	req := &pixrequest.PixTransactionGetRequest{
		// Missing header and channel_request_id
	}

	// Call the API
	resp, err := client.PixTransactionGet(context.Background(), req)

	// Assertions
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Should return validation error
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Expected validation error response")
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error)
	assert.Equal(t, "INVALID_PARAMETERS", errorResp.ErrorSubcode)
	assert.Contains(t, errorResp.ErrorMessage, "Parameter validation failed")
	assert.Contains(t, errorResp.ErrorDetails, "channel_request_id: is required")
}

// Test_shouldReturnSuccessResponseWhenPixTransactionGetGivenValidRequestAndJDSuccess tests successful API call
func Test_shouldReturnSuccessResponseWhenPixTransactionGetGivenValidRequestAndJDSuccess(t *testing.T) {
	// Setup test server
	testServer := fixture.NewTestServer(t)
	defer testServer.Close()

	// Configure mock client for success response
	mockPixPaymentClient := testServer.GetMockPixPaymentClient()
	now := time.Now()
	mockPixPaymentClient.SetResponse("PixTransactionGet", &fixture.APIResponse{
		Success: map[string]interface{}{
			"idReqJdPiConsultada":    "jd-req-456",
			"dtHrReqJdPi":            now.Add(-5 * time.Minute).Format(time.RFC3339),
			"dtHrSituacao":           now.Format(time.RFC3339),
			"stJdPiProc":             0.0, // Success
			"stJdPi":                 9.0, // Success
			"endToEndId":             "E1234567820241225153000000000123",
			"dtHrEfetivacao":         now.Add(-2 * time.Minute).Format(time.RFC3339),
			"tpIniciacao":            1.0, // Key
			"prioridadePagamento":    1.0, // Normal
			"tpPrioridadePagamento":  0.0, // High
			"finalidade":             0.0, // Purchase/Transfer
			"cnpjIniciadorPagamento": **************.0,
			"pagador": map[string]interface{}{
				"ispb":      12345678.0,
				"tpPessoa":  0.0,
				"cpfCnpj":   ***********.0,
				"nome":      "Test Payer",
				"nrAgencia": "0001",
				"tpConta":   0.0,
				"nrConta":   "123456789",
			},
			"recebedor": map[string]interface{}{
				"ispb":      87654321.0,
				"tpPessoa":  0.0,
				"cpfCnpj":   ***********.0,
				"nome":      "Test Payee",
				"nrAgencia": "0001",
				"tpConta":   0.0,
				"nrConta":   "987654321",
			},
			"valor":                  100.50,
			"vlrDetalhe":             []interface{}{},
			"chave":                  "<EMAIL>",
			"idConciliacaoRecebedor": "reconciliation-123",
			"infEntreClientes":       "Test payment",
		},
		ShouldErr: false,
	})

	// Create gRPC client
	client := testServer.CreatePixPaymentServiceClient()

	// Create valid request
	req := &pixrequest.PixTransactionGetRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "test-idempotence-123",
		},
		ChannelRequestId: "E1234567820241225153000000000123",
	}

	// Call the API
	resp, err := client.PixTransactionGet(context.Background(), req)

	// Assertions
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Should return success response
	successResp := resp.GetResponse()
	require.NotNil(t, successResp, "Expected success response")
	assert.Equal(t, "jd-req-456", successResp.ChannelRequestId)
	assert.Equal(t, "E1234567820241225153000000000123", successResp.EndToEndId)
	assert.Equal(t, wallet.PixTransactionIntermediateStatus_SUCCESS, successResp.IntermediateStatus)
	assert.Equal(t, wallet.PixTransactionStatus_TRANSACTION_SUCCESS, successResp.TransactionStatus)
	assert.Equal(t, wallet.PixTransactionType_PIX_KEY, successResp.TransactionType)
	assert.Equal(t, wallet.PixTransactionPriority(1), successResp.LiquidationPriority)
	assert.Equal(t, wallet.PixTransactionPriorityType(0), successResp.PaymentPriority)
	assert.Equal(t, wallet.PixTransactionPurpose_PURCHASE_OR_TRANSFER, successResp.Purpose)
	assert.Equal(t, int64(**************), successResp.InitiatorCnpj)
	assert.Equal(t, 100.50, successResp.Amount)
	assert.Equal(t, "<EMAIL>", successResp.PixKey)
	assert.Equal(t, "reconciliation-123", successResp.PayeeReconciliationId)
	assert.Equal(t, "Test payment", successResp.ClientInfo)

	// Verify payer information
	require.NotNil(t, successResp.PayerHolder)
	assert.Equal(t, wallet.AccountHolderType_NATURAL, successResp.PayerHolder.HolderType)
	assert.Equal(t, "Test Payer", successResp.PayerHolder.HolderName)
	assert.Equal(t, "***********", successResp.PayerHolder.DocumentId)

	// Verify payee information
	require.NotNil(t, successResp.PayeeHolder)
	assert.Equal(t, wallet.AccountHolderType_NATURAL, successResp.PayeeHolder.HolderType)
	assert.Equal(t, "Test Payee", successResp.PayeeHolder.HolderName)
	assert.Equal(t, "***********", successResp.PayeeHolder.DocumentId)

	// Verify timestamps
	assert.NotNil(t, successResp.ChannelRequestTime)
	assert.NotNil(t, successResp.StatusUpdatedTime)
	assert.NotNil(t, successResp.TransactionEffectiveTime)
}

// Test_shouldReturnErrorResponseWhenPixTransactionGetGivenValidRequestButJDFailure tests JD API error handling
func Test_shouldReturnErrorResponseWhenPixTransactionGetGivenValidRequestButJDFailure(t *testing.T) {
	// Setup test server
	testServer := fixture.NewTestServer(t)
	defer testServer.Close()

	// Configure mock client for error response
	mockPixPaymentClient := testServer.GetMockPixPaymentClient()
	mockPixPaymentClient.SetResponse("PixTransactionGet", &fixture.APIResponse{
		Error: &fixture.JDAPIError{
			Codigo:       "JDPI404",
			Mensagem:     "Transaction not found",
			IdCorrelacao: "correlation-123",
		},
		ShouldErr: true,
	})

	// Create gRPC client
	client := testServer.CreatePixPaymentServiceClient()

	// Create valid request
	req := &pixrequest.PixTransactionGetRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "test-idempotence-123",
		},
		ChannelRequestId: "E1234567820241225153000000000123",
	}

	// Call the API
	resp, err := client.PixTransactionGet(context.Background(), req)

	// Assertions
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Should return error response
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Expected error response")
	assert.Equal(t, pixresponse.ErrorCode_INTERNAL_ERROR, errorResp.Error)
	assert.Equal(t, "INTERNAL_SERVER_ERROR", errorResp.ErrorSubcode)
	assert.Contains(t, errorResp.ErrorMessage, "Internal error occurred")
}
