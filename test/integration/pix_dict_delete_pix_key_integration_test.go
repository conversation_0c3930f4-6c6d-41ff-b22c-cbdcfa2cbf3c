package integration

import (
	"context"
	"testing"

	"jdpi-gateway/internal/model"
	"jdpi-gateway/test/integration/fixture"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	// External protocol imports
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// Test_shouldReturnValidationErrorWhenDeletePixKeyGivenInvalidRequest tests parameter validation failure
func Test_shouldReturnValidationErrorWhenDeletePixKeyGivenInvalidRequest(t *testing.T) {
	// Setup test server with default mocks
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create test request with missing PIX key (should cause validation error)
	req := &pixrequest.PixKeyDeleteRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "b1c5e0022e5543dc88c50084ccae82fc",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb:   48756121,
		PixKey: nil, // Missing PIX key should cause validation error
		Reason: wallet.PixKeyDeleteReason_DELETE_CLIENT_REQUEST,
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyDelete(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyDelete should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for missing PIX key")
	require.Nil(t, resp.GetResponse(), "Should not get success response for missing PIX key")

	// Verify error details
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "INVALID_PARAMETERS", errorResp.ErrorSubcode, "Error subcode should indicate invalid parameters")
	assert.Contains(t, errorResp.ErrorMessage, "Parameter validation failed", "Error message should mention validation failure")

	t.Logf("✅ DeletePixKey validation error test completed successfully")
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}

// Test_shouldReturnSuccessResponseWhenDeletePixKeyGivenValidRequestAndJDSuccess tests successful PIX key deletion
func Test_shouldReturnSuccessResponseWhenDeletePixKeyGivenValidRequestAndJDSuccess(t *testing.T) {
	// Setup test server with custom success response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return success response matching JD API format
	successResponse := &model.DeletePixKeyJDResponse{
		PixKey: "27056201-6a62-4296-9761-b5cf940ac103",
	}
	mockPixDictClient.SetResponse("DeletePixKey", &fixture.APIResponse{
		Success:   successResponse,
		ShouldErr: false,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request matching the provided example
	req := &pixrequest.PixKeyDeleteRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "b1c5e0022e5543dc88c50084ccae82fc",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: 48756121,
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EVP,
			KeyValue: "27056201-6a62-4296-9761-b5cf940ac103",
		},
		Reason: wallet.PixKeyDeleteReason_DELETE_CLIENT_REQUEST,
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyDelete(ctx, req)

	// Verify no error occurred
	require.NoError(t, err, "PixKeyDelete should not return an error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got a success response
	successResp := resp.GetResponse()
	require.NotNil(t, successResp, "Should get success response")
	require.Nil(t, resp.GetError(), "Should not get error response")

	// Verify PIX key information
	assert.NotNil(t, successResp.PixKey, "Response should contain PIX key information")
	assert.Equal(t, wallet.PixKeyType_EVP, successResp.PixKey.KeyType, "PIX key type should be EVP")
	assert.Equal(t, "27056201-6a62-4296-9761-b5cf940ac103", successResp.PixKey.KeyValue, "PIX key value should match")

	t.Logf("✅ DeletePixKey success test completed successfully")
	t.Logf("   PIX Key Type: %s", successResp.PixKey.KeyType)
	t.Logf("   PIX Key Value: %s", successResp.PixKey.KeyValue)
}

// Test_shouldReturnErrorResponseWhenDeletePixKeyGivenValidRequestButJDFailure tests JD API failure handling
func Test_shouldReturnErrorResponseWhenDeletePixKeyGivenValidRequestButJDFailure(t *testing.T) {
	// Setup test server with custom error response
	logger := zap.NewNop()
	mockPixDictClient := fixture.NewMockPixDictClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return JD API error
	jdError := &model.JDAPIError{
		Message:       "PIX key not found",
		OriginalError: "The PIX key to be deleted does not exist",
		StatusCode:    404,
		JDError: &model.JDError{
			Codigo:   "PIX_KEY_NOT_FOUND",
			Mensagem: "PIX key not found in the dictionary",
		},
	}
	mockPixDictClient.SetResponse("DeletePixKey", &fixture.APIResponse{
		Error:     jdError,
		ShouldErr: true,
	})

	testServer := fixture.SetupPixDictTestServer(t, mockPixDictClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.PixKeyDeleteRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "b1c5e0022e5543dc88c50084ccae82fc",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		Ispb: 48756121,
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EVP,
			KeyValue: "nonexistent-key-uuid",
		},
		Reason: wallet.PixKeyDeleteReason_DELETE_CLIENT_REQUEST,
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.PixKeyDelete(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "PixKeyDelete should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for JD API failure")
	require.Nil(t, resp.GetResponse(), "Should not get success response for JD API failure")

	// Verify error details match JD API error
	assert.Equal(t, pixresponse.ErrorCode_INTERNAL_ERROR, errorResp.Error, "Error code should indicate internal error for 404")
	assert.Equal(t, "PIX_KEY_NOT_FOUND", errorResp.ErrorSubcode, "Error subcode should match JD error code")
	assert.Contains(t, errorResp.ErrorMessage, "JD API failed", "Error message should contain JD error message")

	t.Logf("✅ DeletePixKey JD API failure test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}
