// Package integration contains integration tests for PIX QRCode operations
package integration

import (
	"context"
	"testing"

	"jdpi-gateway/internal/model"
	"jdpi-gateway/test/integration/fixture"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	// External protocol imports
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// Test_shouldReturnValidationErrorWhenCreateStaticQRCodeGivenInvalidRequest tests parameter validation failure
func Test_shouldReturnValidationErrorWhenCreateStaticQRCodeGivenInvalidRequest(t *testing.T) {
	// Setup test server with default mocks
	logger := zap.NewNop()
	mockPixQRCodeClient := fixture.NewMockPixQRCodeClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	testServer := fixture.SetupQRCodeTestServer(t, mockPixQRCodeClient, mockAuthService)
	defer testServer.Cleanup()

	// Create test request with missing required fields (should cause validation error)
	req := &pixrequest.QRCodeStaticCreateRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "0e85f17b18b048aa93c4116014c5ca8e",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		// Missing required fields: TxId, PixKey, PixKeyOwnerName, PixKeyCity - should cause validation error
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.QRCodeStaticCreate(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "CreateStaticQRCode should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for invalid request")
	require.Nil(t, resp.GetResponse(), "Should not get success response for invalid request")

	// Verify error details
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "INVALID_PARAMETERS", errorResp.ErrorSubcode, "Error subcode should indicate invalid parameters")
	assert.Contains(t, errorResp.ErrorMessage, "Parameter validation failed", "Error message should mention validation failure")

	t.Logf("✅ Validation error test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}

// Test_shouldReturnSuccessResponseWhenCreateStaticQRCodeGivenValidRequestAndJDSuccess tests successful QR code creation
func Test_shouldReturnSuccessResponseWhenCreateStaticQRCodeGivenValidRequestAndJDSuccess(t *testing.T) {
	// Setup test server with custom success response
	logger := zap.NewNop()
	mockPixQRCodeClient := fixture.NewMockPixQRCodeClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return success response
	successResponse := &model.QRCodeStaticCreateJDResponse{
		QRCodeImageBase64:   "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
		QRCodePayloadBase64: "MDAwMjAxMjY1ODAwMTRCUi5HT1YuQkNCLlBJWA==",
	}
	mockPixQRCodeClient.SetResponse("CreateStaticQRCode", &fixture.APIResponse{
		Success:   successResponse,
		ShouldErr: false,
	})

	testServer := fixture.SetupQRCodeTestServer(t, mockPixQRCodeClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.QRCodeStaticCreateRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "0e85f17b18b048aa93c4116014c5ca8e",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		TxId: "test-tx-id-12345",
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EMAIL,
			KeyValue: "<EMAIL>",
		},
		PixKeyOwnerName: "Test User",
		PixKeyCity:      "São Paulo",
		Amount:          100.50,
		Format:          wallet.QRCodeResponseType_BOTH,
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.QRCodeStaticCreate(ctx, req)

	// Verify no error occurred
	require.NoError(t, err, "CreateStaticQRCode should not return an error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got a success response
	successResp := resp.GetResponse()
	require.NotNil(t, successResp, "Should get success response")
	require.Nil(t, resp.GetError(), "Should not get error response")

	// Verify QR code data
	assert.Equal(t, "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==", successResp.QrcodeImageBase64, "QR code image should match")
	assert.Equal(t, "MDAwMjAxMjY1ODAwMTRCUi5HT1YuQkNCLlBJWA==", successResp.QrcodePayloadBase64, "QR code payload should match")

	t.Logf("✅ Success response test completed successfully")
	t.Logf("   QR Code Image Length: %d", len(successResp.QrcodeImageBase64))
	t.Logf("   QR Code Payload Length: %d", len(successResp.QrcodePayloadBase64))
}

// Test_shouldReturnErrorResponseWhenCreateStaticQRCodeGivenValidRequestButJDFailure tests JD API failure handling
func Test_shouldReturnErrorResponseWhenCreateStaticQRCodeGivenValidRequestButJDFailure(t *testing.T) {
	// Setup test server with custom error response
	logger := zap.NewNop()
	mockPixQRCodeClient := fixture.NewMockPixQRCodeClient(logger)
	mockAuthService := fixture.NewMockAuthService(logger)

	// Configure mock to return JD API error
	jdError := &model.JDAPIError{
		Message:       "QR code creation failed",
		OriginalError: "Invalid PIX key provided",
		StatusCode:    400,
		JDError: &model.JDError{
			Codigo:   "INVALID_PIX_KEY",
			Mensagem: "The provided PIX key is invalid",
		},
	}
	mockPixQRCodeClient.SetResponse("CreateStaticQRCode", &fixture.APIResponse{
		Error:     jdError,
		ShouldErr: true,
	})

	testServer := fixture.SetupQRCodeTestServer(t, mockPixQRCodeClient, mockAuthService)
	defer testServer.Cleanup()

	// Create valid test request
	req := &pixrequest.QRCodeStaticCreateRequest{
		Header: &pixrequest.Header{
			IdempotenceId: "0e85f17b18b048aa93c4116014c5ca8e",
			Channel:       pixrequest.ChannelAdapter_A55_JD,
		},
		TxId: "test-tx-id-12345",
		PixKey: &wallet.PixKey{
			KeyType:  wallet.PixKeyType_EMAIL,
			KeyValue: "<EMAIL>",
		},
		PixKeyOwnerName: "Test User",
		PixKeyCity:      "São Paulo",
		Amount:          100.50,
		Format:          wallet.QRCodeResponseType_BOTH,
	}

	// Execute gRPC call
	ctx := context.Background()
	resp, err := testServer.Client.QRCodeStaticCreate(ctx, req)

	// Verify no gRPC error occurred (errors should be in response)
	require.NoError(t, err, "CreateStaticQRCode should not return gRPC error")
	require.NotNil(t, resp, "Response should not be nil")

	// Verify we got an error response (not a success response)
	errorResp := resp.GetError()
	require.NotNil(t, errorResp, "Should get error response for JD API failure")
	require.Nil(t, resp.GetResponse(), "Should not get success response for JD API failure")

	// Verify error details match JD API error
	assert.Equal(t, pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST, errorResp.Error, "Error code should indicate channel request error")
	assert.Equal(t, "INVALID_PIX_KEY", errorResp.ErrorSubcode, "Error subcode should match JD error code")
	assert.Contains(t, errorResp.ErrorMessage, "JD API failed", "Error message should contain JD error message")

	t.Logf("✅ JD API failure test completed successfully")
	t.Logf("   Error Code: %s", errorResp.Error)
	t.Logf("   Error Subcode: %s", errorResp.ErrorSubcode)
	t.Logf("   Error Message: %s", errorResp.ErrorMessage)
}
