# Build stage
FROM public.ecr.aws/docker/library/golang:1.24-alpine3.21 as build

# Set working directory
WORKDIR /app

# Install necessary packages
RUN apk add --no-cache git ca-certificates tzdata protobuf-dev
RUN apk update && apk add gcc libc-dev


# Copy go mod files and external dependencies
COPY go.mod go.sum ./
COPY external/proto ./external/proto

# List external proto files to make sure submodule is loaded
RUN ls -la ./external/proto

# Download dependencies with timeout and retry
RUN go mod download -x

# Install protoc-gen-go and protoc-gen-go-grpc with GOSUMDB disabled for installation
RUN GOSUMDB=off go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
RUN GOSUMDB=off go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# Copy source code
COPY . .

# Build external proto
RUN cd external/proto && rm -rf gen && sh build.sh

# Generate protobuf code for main project proto files
RUN protoc --go_out=. --go_opt=paths=source_relative \
           --go-grpc_out=. --go-grpc_opt=paths=source_relative \
           proto/*.proto

# Build application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-extldflags "-static"' -o jdpi-gateway ./cmd/main.go

# Run stage
FROM public.ecr.aws/docker/library/alpine:3.21

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy binary from build stage
COPY --from=build /app/jdpi-gateway .

# Copy config file template
COPY --from=build /app/config/*.yaml ./config/

# Change file ownership
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Start application with mock mode
CMD ["./jdpi-gateway", "-config=./config/config.yaml"]
