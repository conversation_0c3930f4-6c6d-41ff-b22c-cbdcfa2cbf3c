Pix Adapter Protocol Interface (Protocol Buffers)
==========================================================

Overview
--------

The `pix-adapter` project serves as the central **Protocol Buffers (Protobuf) interface** for our Pix addressing all operations within our PIX-Ecosystem. 
It defines the standardized messages and gRPC services for interacting with our Pix channels (JD, Delbank, and any other in the future).

This module acts as a **contract** for Pix addressing key management. It does **not** contain any business logic or implementation details.
Instead, it provides the necessary definitions that other client systems and service implementations will adhere to.

Role in Pix Architecture
------------------------

This Protobuf interface is crucial for both **direct** and **indirect** Pix participation:

-   **Direct Participation:** For direct participation in Pix, our internal systems will **implement this interface** to communicate directly with the **JDPI (Junta de Dados do Pix e Indiretos)** or other core Pix infrastructure components.

-   **Indirect Participation:** For indirect participation, our internal systems will **implement this interface** to communicate with our **direct participants** (e.g., partner financial institutions) who handle the direct interaction with Pix.

By using a single, well-defined Protobuf interface, we ensure interoperability and maintainability across our diverse Pix integration channels.

Project Structure
-----------------

The module path for this project is `gitlab.pagsmile.com/wallet/ew_pix_adapter_proto`.

```
pix-adapter/
├── go.mod                     # Go module definition for the Protobuf interfaces
├── build.sh                   # Script to generate Go code from .proto files
├── proto/                     # All Proto Declarations
│   ├── wallet/                # Common ENUMS for wallet project
│   │   └── wallet.proto       
│   └── services/              # Defines the gRPC interface
│       └── services.proto     
└── gen/                       # Generated Go code (automatically created by build.sh)
    └── go/
        ├── wallet/            
        └── services/          

```


Usage
-----

### Prerequisites

Before you begin, ensure you have:

-   **Go (1.20 or higher)** installed.

-   **Protocol Buffers Compiler (`protoc`)** installed.

-   **Go Protobuf plugins (`protoc-gen-go` and `protoc-gen-go-grpc`)** installed. You can install them by running:

    ```
    go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

    ```

    Ensure your `GOPATH/bin` is in your system's `PATH` environment variable.

### 1\. Cloning the Repository & Generate Code

First, clone this repository to your local machine:

```
<NAME_EMAIL>:wallet/ew_pix_adapter_proto.git
cd ew_pix_adapter_proto
bash build.sh
```
**Always run this script after any changes to the `.proto` files.**

### 2\. Go Module Configuration for Private Repository

Since `gitlab.pagsmile.com/wallet/ew_pix_adapter_proto` is a private repository, Go needs to be explicitly told not to look for it on public proxies or checksum databases.

**Before running `go mod tidy` or `go run` in any project that consumes this module, you MUST set the following environment variables in your terminal session:**

```
export GOPRIVATE="gitlab.pagsmile.com"
export GONOSUMDB="gitlab.pagsmile.com"

```

**To make these settings persistent**, add them to your shell's profile file (e.g., `~/.bashrc`, `~/.zshrc`, `~/.profile`) and then `source` the file.

### 3\. Git Authentication for Private Repository

Go uses your local Git client to fetch modules. For private repositories, your Git client needs to be configured to authenticate with GitLab.

#### Solution: Configure Git for SSH (Recommended for Automation)

1.  **Generate SSH Key Pair:** If you don't have one, run `ssh-keygen -t rsa -b 4096 -C "<EMAIL>"`.

2.  **Add Public SSH Key to GitLab:** Go to GitLab -> User Settings -> SSH Keys and add your generated public key (`~/.ssh/id_rsa.pub`).

3.  **Configure `~/.ssh/config`:**

    ```
    Host gitlab.pagsmile.com
      Hostname gitlab.pagsmile.com
      User git
      IdentityFile ~/.ssh/id_rsa # Or the path to your private key

    ```

4.  **Test SSH Connection:**  `ssh -T ***********************` (You should see a welcome message, not a password prompt).

5.  **(Optional, but useful) Configure Git to prefer SSH for this domain:**

    ```
    git config --global url."***********************:".insteadOf "https://gitlab.pagsmile.com/"

    ```

### 4\. Consuming the Module (Example with `testApp`)

In a separate Go project:

1.  **Initialize its own Go module:**

    ```
    cd /path/to/your/testApp
    go mod init testapp

    ```

2**Import the generated packages in your Go source files:**

    ```
    import (
        // For messages
        pb "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
        // For services
        servicespb "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services"
    )

    ```

3**Run `go mod tidy` in the consumer project:** This will add the `require` directive for `gitlab.pagsmile.com/wallet/ew_pix_adapter_proto` to its `go.mod`.

    ```
    cd /path/to/your/testApp
    go mod tidy

    ```

4**Build and run your consumer application.**

Contributing
------------

When making changes to the `.proto` files in this `pix-adapter` repository:

1.  **Modify the `.proto` files** as needed.

2.  **Run `./build.sh`** from the `pix-adapter` root directory. This regenerates the Go source code.

3.  **Review the generated Go files** (`gen/go/...`) for any unexpected changes.

4.  **Commit both your `.proto` changes AND the generated Go code** to the repository since we don't have any CI/CD for this project yet.
    

Questions?
----------

For any questions or further assistance, please contact **<EMAIL>** or **<EMAIL>** 