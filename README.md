# JDPI Gateway

JDPI Gateway is a gRPC-based microservice for interacting with the JD API, providing features such as access token retrieval.

## Features

- **gRPC API**: High-performance gRPC interface
- **Access Token Management**: Automatically obtains and caches JD API access tokens
- **KMS Integration**: Securely manages API keys via AWS KMS
- **Mock Mode**: Supports mock mode for development and testing environments
- **Logging**: Structured logging and request tracing
- **Health Check**: Built-in health check mechanism
- **Graceful Shutdown**: Supports graceful shutdown and signal handling

## Architecture

The project adopts a layered architecture:

```
├── cmd/                # Application entry point
├── config/             # Configuration management
├── internal/
│   ├── client/         # External service client
│   ├── handler/        # gRPC handler
│   ├── server/         # gRPC server
│   ├── service/        # Business logic layer
│   ├── model/          # Data models
│   └── interceptor/    # gRPC interceptors
├── proto/              # Protocol Buffers definitions
├── pkg/                # Reusable packages
└── docs/               # Documentation
```

## Quick Start

### Prerequisites

- Go 1.24
- Protocol Buffers compiler (optional, generated code is included)

### Install Dependencies

```bash
make deps
```

### Build Application

```bash
make build
```

### Run Application

#### Mock Mode (Recommended for Development)

```bash
make run-mock
```

#### Normal Mode
1. Edit the config file and fill in the actual configuration values

2. Run the application:
```bash
make run
```

## Configuration

### Config File Structure

```yaml
server:
  host: "0.0.0.0"        # Server listen address
  port: 8080             # Server listen port

jd:
  base_url: "https://api.jd.example.com"        # JD API base URL
  grant_type: "client_credentials"              # OAuth2 grant type
  scope: "dict_api,qrcode_api,spi_api,auth_api" # API scope
  client_id: "abcde"                            # Client ID
  client_secret: "xyzopq"                       # Client Secret
  mock_mode: false                              # Enable mock mode
  timeout_seconds: 30                           # Request timeout


log:
  level: "info"          # Log level (debug, info, warn, error)
  format: "json"         # Log format (json, console)
  output_path: "stdout"  # Log output path
```

### Environment Variables

Configuration can also be set via environment variables, using the format `JDPI_<SECTION>_<KEY>`:

```bash
export JDPI_SERVER_PORT=8080
export JDPI_JD_MOCK_MODE=true
export JDPI_LOG_LEVEL=debug
```

## API

### GetAccessToken

Retrieve JD API access token.

**Request**:
```protobuf
message GetAccessTokenRequest {
  string request_id = 1;  // Request ID (optional)
}
```

**Response**:
```protobuf
message GetAccessTokenResponse {
  ResponseStatus status = 1;    // Response status
  AccessTokenData data = 2;     // Access token data
}
```

## Development Guide

### Generate Protocol Buffers Code

```bash
make proto
```

### Run Tests

```bash
make test
```

### Code Formatting

```bash
make fmt
```

### Linting

```bash
make lint
```

### Test Coverage

```bash
make test-coverage
```

## Docker Deployment

### Build Docker Image

```bash
make docker-build
```

### Run Docker Container

```bash
make docker-run
```

## Monitoring and Logging

### Log Format

The application uses structured logging, supporting both JSON and console formats:

```json
{
  "level": "info",
  "ts": "2024-01-01T12:00:00.000Z",
  "caller": "handler/jdpi_handler.go:45",
  "msg": "Processing GetAccessToken request",
  "request_id": "req_1704110400000000000",
  "client_id": "test_client"
}
```

### Health Check

The application provides a health check interface, which can be used for container orchestration and load balancers:

```bash
# gRPC health check (requires grpc_health_probe tool)
grpc_health_probe -addr=localhost:8080
```
