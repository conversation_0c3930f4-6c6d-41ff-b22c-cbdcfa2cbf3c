package client

import (
	"context"
	"encoding/json"
	"fmt"

	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

// PixQRCodeClient PIX QRCode client interface
type PixQRCodeClient interface {
	CreateStaticQRCode(ctx context.Context, req *model.QRCodeStaticCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeStaticCreateJDResponse, error)
	CreateDynamicQRCode(ctx context.Context, req *model.QRCodeDynamicCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicCreateJDResponse, error)
	DecodeQRCodeByPayload(ctx context.Context, req *model.QRCodeGetByPayloadJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeGetByPayloadJDResponse, error)
	DecodeDynamicQRCodeByUrl(ctx context.Context, req *model.QRCodeDynamicDecodeByUrlJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicDecodeByUrlJDResponse, error)
}

// JDPixQRCodeClient JD PIX QRCode client implementation
type JDPixQRCodeClient struct {
	jdpiClient    JDPIClient
	tokenProvider TokenProvider
	logger        *zap.Logger
}

// NewJDPixQRCodeClient creates a new JD PIX QRCode client
func NewJDPixQRCodeClient(jdpiClient JDPIClient, tokenProvider TokenProvider, logger *zap.Logger) *JDPixQRCodeClient {
	return &JDPixQRCodeClient{
		jdpiClient:    jdpiClient,
		tokenProvider: tokenProvider,
		logger:        logger,
	}
}

// createLogger creates a logger with trace ID and idempotence ID
func (c *JDPixQRCodeClient) createLogger(ctx context.Context, idempotenceID string) *zap.Logger {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	return c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
		zap.String("component", "JDPixQRCodeClient"),
	)
}

// createQRCodeHeaders converts QRCodeJDHeaders to HTTP headers map
func (c *JDPixQRCodeClient) createQRCodeHeaders(qrHeaders *model.QRCodeJDHeaders) map[string]string {
	if qrHeaders == nil {
		return nil
	}

	headers := make(map[string]string)
	if qrHeaders.IdempotenceID != "" {
		headers["Chave-Idempotencia"] = qrHeaders.IdempotenceID
	}
	if qrHeaders.EndToEndID != "" {
		headers["PI-EndToEndId"] = qrHeaders.EndToEndID
	}
	if qrHeaders.PayerID != "" {
		headers["PI-PayerId"] = qrHeaders.PayerID
	}
	return headers
}

// sendAuthenticatedRequest sends an authenticated request to JD API
func (c *JDPixQRCodeClient) sendAuthenticatedRequest(ctx context.Context, method, url string, request interface{}, idempotenceID string, headers map[string]string) (*JDErrorResponse, string, error) {
	logger := c.createLogger(ctx, idempotenceID)

	// Get access token
	token, err := c.tokenProvider.GetAccessToken(ctx, idempotenceID)
	if err != nil {
		logger.Error("Failed to get access token", zap.Error(err))
		return nil, "", fmt.Errorf("failed to get access token: %w", err)
	}

	if token == nil || token.AccessToken == "" {
		return nil, "", fmt.Errorf("access token is empty")
	}

	accessToken := token.AccessToken

	// Send request
	var resp *resty.Response
	switch method {
	case "POST":
		resp, err = c.jdpiClient.PostWithAuth(ctx, url, request, idempotenceID, accessToken, headers)
	case "PUT":
		resp, err = c.jdpiClient.PutWithAuth(ctx, url, request, idempotenceID, accessToken, headers)
	case "GET":
		resp, err = c.jdpiClient.GetWithAuth(ctx, url, idempotenceID, accessToken, headers)
	default:
		return nil, "", fmt.Errorf("unsupported HTTP method: %s", method)
	}

	if err != nil {
		logger.Error("JD API request failed", zap.Error(err))
		return nil, "", err
	}

	responseBody := string(resp.Body())

	// Check for JD API errors
	if resp.StatusCode() >= 400 {
		logger.Error("JD API returned error",
			zap.Int("status_code", resp.StatusCode()),
			zap.String("response_body", responseBody),
		)

		// Parse JD error response using the error handler
		jdErrorResp := ParseJDError(resp, logger)
		return jdErrorResp, responseBody, nil
	}

	return nil, responseBody, nil
}

// CreateStaticQRCode creates a static QR code via JD API
func (c *JDPixQRCodeClient) CreateStaticQRCode(ctx context.Context, req *model.QRCodeStaticCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeStaticCreateJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	logger := c.createLogger(ctx, idempotenceID)

	// Create QRCode specific headers
	qrHeaders := c.createQRCodeHeaders(headers)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().QRCodeStaticCreate, req, idempotenceID, qrHeaders)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"CreateStaticQRCode failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			idempotenceID,
			jdErrorResp.JDError,
		)
	}

	// Parse successful response
	var response model.QRCodeStaticCreateJDResponse
	if err := json.Unmarshal([]byte(responseBody), &response); err != nil {
		logger.Error("Failed to parse CreateStaticQRCode response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully created static QR code")
	return &response, nil
}

// CreateDynamicQRCode creates a dynamic QR code via JD API
func (c *JDPixQRCodeClient) CreateDynamicQRCode(ctx context.Context, req *model.QRCodeDynamicCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicCreateJDResponse, error) {
	logger := c.createLogger(ctx, headers.IdempotenceID)

	// Create QRCode specific headers
	qrHeaders := c.createQRCodeHeaders(headers)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().QRCodeDynamicCreate, req, headers.IdempotenceID, qrHeaders)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"CreateDynamicQRCode failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			headers.IdempotenceID,
			jdErrorResp.JDError,
		)
	}

	// Parse successful response
	var response model.QRCodeDynamicCreateJDResponse
	if err := json.Unmarshal([]byte(responseBody), &response); err != nil {
		logger.Error("Failed to parse CreateDynamicQRCode response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully created dynamic QR code", zap.String("document_id", response.DocumentID))
	return &response, nil
}

// DecodeQRCodeByPayload gets QR code information by payload via JD API
func (c *JDPixQRCodeClient) DecodeQRCodeByPayload(ctx context.Context, req *model.QRCodeGetByPayloadJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeGetByPayloadJDResponse, error) {
	logger := c.createLogger(ctx, headers.IdempotenceID)

	// Create QRCode specific headers (required for this API)
	qrHeaders := c.createQRCodeHeaders(headers)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().QRCodeGetByPayload, req, headers.IdempotenceID, qrHeaders)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"DecodeQRCodeByPayload failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			headers.IdempotenceID,
			jdErrorResp.JDError,
		)
	}

	// Parse successful response
	var response model.QRCodeGetByPayloadJDResponse
	if err := json.Unmarshal([]byte(responseBody), &response); err != nil {
		logger.Error("Failed to parse DecodeQRCodeByPayload response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully got QR code by payload", zap.String("end_to_end_id", response.EndToEndID))
	return &response, nil
}

// DecodeDynamicQRCodeByUrl decodes dynamic QR code by URL via JD API
func (c *JDPixQRCodeClient) DecodeDynamicQRCodeByUrl(ctx context.Context, req *model.QRCodeDynamicDecodeByUrlJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicDecodeByUrlJDResponse, error) {
	logger := c.createLogger(ctx, headers.IdempotenceID)

	// Create QRCode specific headers (required for this API)
	qrHeaders := c.createQRCodeHeaders(headers)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().QRCodeDynamicDecodeByUrl, req, headers.IdempotenceID, qrHeaders)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"DecodeDynamicQRCodeByUrl failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			headers.IdempotenceID,
			jdErrorResp.JDError,
		)
	}

	// Parse successful response
	var response model.QRCodeDynamicDecodeByUrlJDResponse
	if err := json.Unmarshal([]byte(responseBody), &response); err != nil {
		logger.Error("Failed to parse DecodeDynamicQRCodeByUrl response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("Successfully decoded dynamic QR code by URL", zap.String("end_to_end_id", response.EndToEndID))
	return &response, nil
}
