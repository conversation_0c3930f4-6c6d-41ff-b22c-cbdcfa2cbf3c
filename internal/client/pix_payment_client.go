package client

import (
	"context"
	"encoding/json"
	"fmt"

	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

// PixPaymentClient PIX Payment client interface
type PixPaymentClient interface {
	PixOutConfirm(ctx context.Context, req *model.PixOutConfirmJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixOutConfirmJDResponse, error)
	PixTransactionGet(ctx context.Context, req *model.PixTransactionGetJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixTransactionGetJDResponse, error)
}

// JDPixPaymentClient JD PIX Payment client implementation
type JDPixPaymentClient struct {
	jdpiClient    JDPIClient
	tokenProvider TokenProvider
	logger        *zap.Logger
}

// NewJDPixPaymentClient creates a new JD PIX Payment client
func NewJDPixPaymentClient(jdpiClient JDPIClient, tokenProvider TokenProvider, logger *zap.Logger) *JDPixPaymentClient {
	return &JDPixPaymentClient{
		jdpiClient:    jdpiClient,
		tokenProvider: tokenProvider,
		logger:        logger,
	}
}

// createLogger creates a logger with trace ID and idempotence ID
func (c *JDPixPaymentClient) createLogger(ctx context.Context, idempotenceID string) *zap.Logger {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	return c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
		zap.String("component", "JDPixPaymentClient"),
	)
}

// createPixPaymentHeaders converts PixPaymentJDHeaders to HTTP headers map
func (c *JDPixPaymentClient) createPixPaymentHeaders(paymentHeaders *model.PixPaymentJDHeaders) map[string]string {
	if paymentHeaders == nil {
		return nil
	}

	headers := make(map[string]string)
	if paymentHeaders.IdempotenceID != "" {
		headers["Chave-Idempotencia"] = paymentHeaders.IdempotenceID
	}
	if paymentHeaders.EndToEndID != "" {
		headers["PI-EndToEndId"] = paymentHeaders.EndToEndID
	}
	return headers
}

// sendAuthenticatedRequest sends an authenticated request to JD API
func (c *JDPixPaymentClient) sendAuthenticatedRequest(ctx context.Context, method, url string, requestBody interface{}, idempotenceID string, additionalHeaders map[string]string) (*JDErrorResponse, []byte, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Get access token
	token, err := c.tokenProvider.GetAccessToken(ctx, traceID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// Send request based on method
	var resp *resty.Response
	switch method {
	case "POST":
		resp, err = c.jdpiClient.PostWithAuth(ctx, url, requestBody, idempotenceID, token.AccessToken, additionalHeaders)
	case "GET":
		resp, err = c.jdpiClient.GetWithAuth(ctx, url, idempotenceID, token.AccessToken, additionalHeaders)
	case "PUT":
		resp, err = c.jdpiClient.PutWithAuth(ctx, url, requestBody, idempotenceID, token.AccessToken, additionalHeaders)
	default:
		return nil, nil, fmt.Errorf("unsupported HTTP method: %s", method)
	}

	if err != nil {
		return nil, nil, err
	}

	// Check for JD API error response
	if resp.StatusCode() >= 400 {
		jdError := ParseJDError(resp, c.logger)
		return jdError, nil, nil
	}

	return nil, resp.Body(), nil
}

// PixOutConfirm confirms a PIX payment via JD API
func (c *JDPixPaymentClient) PixOutConfirm(ctx context.Context, req *model.PixOutConfirmJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixOutConfirmJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	logger := c.createLogger(ctx, idempotenceID)

	// Create PixPayment specific headers
	paymentHeaders := c.createPixPaymentHeaders(headers)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().PixOutConfirm, req, idempotenceID, paymentHeaders)
	if err != nil {
		logger.Error("Failed to send PixOutConfirm request", zap.Error(err))
		return nil, err
	}

	// Check for JD API error response
	if jdErrorResp != nil {
		logger.Error("JD API returned error for PixOutConfirm",
			zap.String("error_message", jdErrorResp.GetErrorMessage()),
			zap.String("detailed_error", jdErrorResp.GetDetailedErrorMessage()))
		return nil, fmt.Errorf("JD API error: %s", jdErrorResp.GetErrorMessage())
	}

	// Parse success response
	var response model.PixOutConfirmJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to unmarshal PixOutConfirm response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("PixOutConfirm request completed successfully",
		zap.String("client_request_id", response.ClientRequestID),
		zap.String("jd_request_id", response.JDRequestID),
		zap.String("end_to_end_id", response.EndToEndID))

	return &response, nil
}

// PixTransactionGet gets PIX transaction status by EndToEndId via JD API
func (c *JDPixPaymentClient) PixTransactionGet(ctx context.Context, req *model.PixTransactionGetJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixTransactionGetJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	logger := c.createLogger(ctx, idempotenceID)

	// Create PixPayment specific headers
	paymentHeaders := c.createPixPaymentHeaders(headers)

	// For GET request, we need to construct URL with EndToEndId as path parameter
	url := fmt.Sprintf("%s/%s", c.jdpiClient.GetURLs().PixTransactionGet, req.JDRequestID)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "GET", url, nil, idempotenceID, paymentHeaders)
	if err != nil {
		logger.Error("Failed to send PixTransactionGet request", zap.Error(err))
		return nil, err
	}

	// Check for JD API error response
	if jdErrorResp != nil {
		logger.Error("JD API returned error for PixTransactionGet",
			zap.String("error_message", jdErrorResp.GetErrorMessage()),
			zap.String("detailed_error", jdErrorResp.GetDetailedErrorMessage()))
		return nil, fmt.Errorf("JD API error: %s", jdErrorResp.GetErrorMessage())
	}

	// Parse success response
	var response model.PixTransactionGetJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to unmarshal PixTransactionGet response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	logger.Info("PixTransactionGet request completed successfully",
		zap.String("jd_request_id", response.JDRequestID),
		zap.String("end_to_end_id", response.EndToEndID),
		zap.Int("final_status", response.FinalStatus))

	return &response, nil
}
