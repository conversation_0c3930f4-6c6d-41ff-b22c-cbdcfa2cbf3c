package client

import (
	"context"
	"encoding/json"
	"fmt"
	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"
	"net/url"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

// PixDictClient PIX Dict operations client interface
type PixDictClient interface {
	CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest, idempotenceID string) (*model.CreatePixKeyJDResponse, error)
	UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest, idempotenceID string) (*model.UpdatePixKeyJDResponse, error)
	DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest, idempotenceID string) (*model.DeletePixKeyJDResponse, error)
	QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest, idempotenceID string) (*model.QueryPixKeyJDResponse, error)
	VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest, idempotenceID string) (*model.VerifyPixKeyExistenceJDResponse, error)
	ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest, idempotenceID string) (*model.ListPixKeysJDResponse, error)
	NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest, idempotenceID string) (*model.NotifyAccountClosureJDResponse, error)
	NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest, idempotenceID string) (*model.NotifyOwnershipLinkClosureJDResponse, error)
}

// TokenProvider interface for getting access tokens
type TokenProvider interface {
	GetAccessToken(ctx context.Context, requestID string) (*model.AccessToken, error)
}

// JDPixDictClient JD PIX Dict client implementation
type JDPixDictClient struct {
	jdpiClient    JDPIClient
	tokenProvider TokenProvider
	logger        *zap.Logger
}

// NewJDPixDictClient creates a new JD PIX Dict client
func NewJDPixDictClient(jdpiClient JDPIClient, tokenProvider TokenProvider, logger *zap.Logger) *JDPixDictClient {
	return &JDPixDictClient{
		jdpiClient:    jdpiClient,
		tokenProvider: tokenProvider,
		logger:        logger,
	}
}

// createLogger creates a logger with trace_id and idempotence_id
func (c *JDPixDictClient) createLogger(ctx context.Context, idempotenceID string) *zap.Logger {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// If idempotenceID is empty, generate a new one
	if idempotenceID == "" {
		idempotenceID = c.jdpiClient.GenerateIdempotenceId()
	}

	return c.logger.With(
		zap.String("idempotence_id", idempotenceID),
		zap.String("trace_id", traceID),
	)
}

// getAccessTokenWithAuth gets access token from token provider
func (c *JDPixDictClient) getAccessTokenWithAuth(ctx context.Context) (string, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Get access token from token provider
	token, err := c.tokenProvider.GetAccessToken(ctx, traceID)
	if err != nil {
		return "", fmt.Errorf("failed to get access token from token provider: %w", err)
	}

	if token == nil || token.AccessToken == "" {
		return "", fmt.Errorf("access token is empty")
	}

	return token.AccessToken, nil
}

// sendAuthenticatedRequest sends a request with authentication headers
func (c *JDPixDictClient) sendAuthenticatedRequest(ctx context.Context, method, url string, request interface{}, idempotenceID string, headers map[string]string) (*JDErrorResponse, []byte, error) {
	// Get access token
	accessToken, err := c.getAccessTokenWithAuth(ctx)
	if err != nil {
		return nil, nil, err
	}

	// If idempotenceID is empty, generate a new one
	if idempotenceID == "" {
		idempotenceID = c.jdpiClient.GenerateIdempotenceId()
	}

	logger := c.createLogger(ctx, idempotenceID)

	// Prepare request with authentication
	var resp *resty.Response
	switch method {
	case "POST":
		resp, err = c.jdpiClient.PostWithAuth(ctx, url, request, idempotenceID, accessToken, headers)
	case "PUT":
		resp, err = c.jdpiClient.PutWithAuth(ctx, url, request, idempotenceID, accessToken, headers)
	case "GET":
		resp, err = c.jdpiClient.GetWithAuth(ctx, url, idempotenceID, accessToken, headers)
	default:
		return nil, nil, fmt.Errorf("unsupported HTTP method: %s", method)
	}

	if err != nil {
		return nil, nil, fmt.Errorf("failed to send request: %w", err)
	}

	if !resp.IsSuccess() {
		errorResponse := ParseJDError(resp, logger)
		return errorResponse, nil, nil
	}

	return nil, resp.Body(), nil
}

// CreatePixKey creates a PIX key via JD API
func (c *JDPixDictClient) CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest, idempotenceID string) (*model.CreatePixKeyJDResponse, error) {
	logger := c.createLogger(ctx, idempotenceID)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().CreatePixKey, req, idempotenceID, nil)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"CreatePixKey failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			idempotenceID,
			jdErrorResp.JDError,
		)
	}

	var response model.CreatePixKeyJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}

// UpdatePixKey updates a PIX key via JD API
func (c *JDPixDictClient) UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest, idempotenceID string) (*model.UpdatePixKeyJDResponse, error) {
	logger := c.createLogger(ctx, idempotenceID)

	url := fmt.Sprintf(c.jdpiClient.GetURLs().UpdatePixKey, req.PixKey)
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "PUT", url, req, idempotenceID, nil)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"UpdatePixKey failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			idempotenceID,
			jdErrorResp.JDError,
		)
	}

	var response model.UpdatePixKeyJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}

// DeletePixKey deletes a PIX key via JD API
func (c *JDPixDictClient) DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest, idempotenceID string) (*model.DeletePixKeyJDResponse, error) {
	logger := c.createLogger(ctx, idempotenceID)

	url := fmt.Sprintf(c.jdpiClient.GetURLs().DeletePixKey, req.PixKey)
	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", url, req, idempotenceID, nil)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"DeletePixKey failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			idempotenceID,
			jdErrorResp.JDError,
		)
	}

	var response model.DeletePixKeyJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}

// QueryPixKey queries a PIX key via JD API
func (c *JDPixDictClient) QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest, idempotenceID string) (*model.QueryPixKeyJDResponse, error) {
	logger := c.createLogger(ctx, idempotenceID)

	// Build query parameters
	queryParams := url.Values{}
	if req.EndToEndId != "" {
		queryParams.Set("endToEndId", req.EndToEndId)
	}
	if req.PlayerId != "" {
		queryParams.Set("playerId", req.PlayerId)
	}

	requestURL := fmt.Sprintf(c.jdpiClient.GetURLs().QueryPixKey, req.PixKey)
	if len(queryParams) > 0 {
		requestURL += "?" + queryParams.Encode()
	}

	// Prepare custom headers for QueryPixKey
	customHeaders := make(map[string]string)
	customHeaders["PI-PayerId"] = req.PlayerId
	if req.EndToEndId != "" {
		customHeaders["PI-EndToEndId"] = req.EndToEndId
	}

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "GET", requestURL, nil, idempotenceID, customHeaders)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"QueryPixKey failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			idempotenceID,
			jdErrorResp.JDError,
		)
	}

	var response model.QueryPixKeyJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}

// VerifyPixKeyExistence verifies PIX key existence via JD API
func (c *JDPixDictClient) VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest, idempotenceID string) (*model.VerifyPixKeyExistenceJDResponse, error) {
	logger := c.createLogger(ctx, idempotenceID)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().VerifyPixKeyExistence, req, idempotenceID, nil)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"VerifyPixKeyExistence failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			idempotenceID,
			jdErrorResp.JDError,
		)
	}

	var response model.VerifyPixKeyExistenceJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}

// ListPixKeys lists PIX keys via JD API
func (c *JDPixDictClient) ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest, idempotenceID string) (*model.ListPixKeysJDResponse, error) {
	logger := c.createLogger(ctx, idempotenceID)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().ListPixKeys, req, idempotenceID, nil)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"ListPixKeys failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			idempotenceID,
			jdErrorResp.JDError,
		)
	}

	var response model.ListPixKeysJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}

// NotifyAccountClosure notifies account closure via JD API
func (c *JDPixDictClient) NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest, idempotenceID string) (*model.NotifyAccountClosureJDResponse, error) {
	logger := c.createLogger(ctx, idempotenceID)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().NotifyAccountClosure, req, idempotenceID, nil)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"NotifyAccountClosure failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			idempotenceID,
			jdErrorResp.JDError,
		)
	}

	var response model.NotifyAccountClosureJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}

// NotifyOwnershipLinkClosure notifies ownership link closure via JD API
func (c *JDPixDictClient) NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest, idempotenceID string) (*model.NotifyOwnershipLinkClosureJDResponse, error) {
	logger := c.createLogger(ctx, idempotenceID)

	jdErrorResp, responseBody, err := c.sendAuthenticatedRequest(ctx, "POST", c.jdpiClient.GetURLs().NotifyOwnershipLinkClosure, req, idempotenceID, nil)

	if err != nil {
		return nil, err
	}

	if jdErrorResp != nil {
		return nil, model.NewJDAPIError(
			"NotifyOwnershipLinkClosure failed",
			jdErrorResp.GetErrorMessage(),
			jdErrorResp.GetRawResponseForDetails(),
			jdErrorResp.StatusCode,
			idempotenceID,
			jdErrorResp.JDError,
		)
	}

	var response model.NotifyOwnershipLinkClosureJDResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		logger.Error("Failed to parse response", zap.Error(err))
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &response, nil
}
