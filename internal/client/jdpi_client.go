package client

import (
	"context"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/google/uuid"

	"jdpi-gateway/config"
	"jdpi-gateway/internal/interceptor"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

// JDPIURLs contains all JD API endpoints
type JDPIURLs struct {
	// Auth endpoints
	AuthToken string

	// PIX Dict endpoints
	CreatePixKey               string
	UpdatePixKey               string
	DeletePixKey               string
	NotifyAccountClosure       string
	NotifyOwnershipLinkClosure string
	ListPixKeys                string
	QueryPixKey                string
	VerifyPixKeyExistence      string

	// PIX QRCode endpoints
	QRCodeStaticCreate       string
	QRCodeDynamicCreate      string
	QRCodeGetByPayload       string
	QRCodeDynamicDecodeByUrl string

	// PIX Payment endpoints
	PixOutConfirm     string
	PixTransactionGet string
}

// NewJDPIURLs creates a new JD API URLs configuration
func NewJDPIURLs() *JDPIURLs {
	return &JDPIURLs{
		// Auth endpoints
		AuthToken: "/auth/jdpi/connect/token",

		// PIX Dict endpoints
		CreatePixKey:               "/chave-gestao-api/jdpi/dict/api/v2/incluir",
		UpdatePixKey:               "/chave-gestao-api/jdpi/dict/api/v2/%s", // %s = pixKey
		QueryPixKey:                "/chave-gestao-api/jdpi/dict/api/v2/%s", // %s = pixKey
		VerifyPixKeyExistence:      "/chave-gestao-api/jdpi/dict/api/v2/verificar",
		DeletePixKey:               "/chave-gestao-api/jdpi/dict/api/v2/%s/excluir", // %s = pixKey
		ListPixKeys:                "/chave-gestao-api/jdpi/dict/api/v2/listar/chave",
		NotifyAccountClosure:       "/chave-gestao-api/jdpi/dict/api/v2/encerrar-conta",
		NotifyOwnershipLinkClosure: "/chave-gestao-api/jdpi/dict/api/v2/excluir-titular",

		// PIX QRCode endpoints
		QRCodeStaticCreate:       "/qrcode-api/jdpi/qrcode/api/v2/estatico",
		QRCodeDynamicCreate:      "/qrcode-api/jdpi/qrcode/api/v2/dinamico-imediato",
		QRCodeGetByPayload:       "/qrcode-api/jdpi/qrcode/api/v2/decodificar",
		QRCodeDynamicDecodeByUrl: "/qrcode-api/jdpi/qrcode/api/v2/decodificar/url",

		// PIX Payment endpoints
		PixOutConfirm:     "/pagamento-api/jdpi/pagamento/api/v2/efetivar",
		PixTransactionGet: "/pagamento-api/jdpi/pagamento/api/v2/consultar",
	}
}

// JDPIClient basic JD API HTTP client interface
type JDPIClient interface {
	// Generic HTTP methods
	PostForm(ctx context.Context, url string, formData map[string]string, idempotenceID string) (*resty.Response, error)

	// Authenticated HTTP methods with custom headers
	PostWithAuth(ctx context.Context, url string, request interface{}, idempotenceID, accessToken string, headers map[string]string) (*resty.Response, error)
	PutWithAuth(ctx context.Context, url string, request interface{}, idempotenceID, accessToken string, headers map[string]string) (*resty.Response, error)
	GetWithAuth(ctx context.Context, url string, idempotenceID, accessToken string, headers map[string]string) (*resty.Response, error)

	// URL management
	GetURLs() *JDPIURLs

	// Idempotence ID generation
	GenerateIdempotenceId() string
}

// HTTPJDPIClient HTTP implementation of basic JD API client
type HTTPJDPIClient struct {
	client *resty.Client
	config *config.Config
	logger *zap.Logger
	urls   *JDPIURLs
}

// NewHTTPJDPIClient creates a new HTTP JD API client
func NewHTTPJDPIClient(cfg *config.Config, logger *zap.Logger) *HTTPJDPIClient {
	client := resty.New()
	client.SetBaseURL(cfg.JD.BaseURL)
	client.SetTimeout(time.Duration(cfg.JD.TimeoutSecs) * time.Second)

	return &HTTPJDPIClient{
		client: client,
		config: cfg,
		logger: logger,
		urls:   NewJDPIURLs(),
	}
}

// GetURLs returns the JD API URLs configuration
func (c *HTTPJDPIClient) GetURLs() *JDPIURLs {
	return c.urls
}

// PostForm sends a POST request with form data to JD API
func (c *HTTPJDPIClient) PostForm(ctx context.Context, url string, formData map[string]string, idempotenceID string) (*resty.Response, error) {
	return c.sendRequestWithHeaders(ctx, "POST", url, nil, idempotenceID, formData, "", nil)
}

// PostWithAuth sends a POST request with authentication and custom headers to JD API
func (c *HTTPJDPIClient) PostWithAuth(ctx context.Context, url string, request interface{}, idempotenceID, accessToken string, headers map[string]string) (*resty.Response, error) {
	return c.sendRequestWithHeaders(ctx, "POST", url, request, idempotenceID, nil, accessToken, headers)
}

// PutWithAuth sends a PUT request with authentication and custom headers to JD API
func (c *HTTPJDPIClient) PutWithAuth(ctx context.Context, url string, request interface{}, idempotenceID, accessToken string, headers map[string]string) (*resty.Response, error) {
	return c.sendRequestWithHeaders(ctx, "PUT", url, request, idempotenceID, nil, accessToken, headers)
}

// GetWithAuth sends a GET request with authentication and custom headers to JD API
func (c *HTTPJDPIClient) GetWithAuth(ctx context.Context, url string, idempotenceID, accessToken string, headers map[string]string) (*resty.Response, error) {
	return c.sendRequestWithHeaders(ctx, "GET", url, nil, idempotenceID, nil, accessToken, headers)
}

// sendRequestWithHeaders is the core method that handles all HTTP requests to JD API with custom headers
func (c *HTTPJDPIClient) sendRequestWithHeaders(ctx context.Context, method, url string, request interface{}, idempotenceID string, formData map[string]string, accessToken string, headers map[string]string) (*resty.Response, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
		zap.String("method", method),
		zap.String("url", url),
	)

	// Log request details
	logger.Info("Sending request to JD API with custom headers",
		zap.String("method", method),
		zap.String("url", url),
		zap.String("idempotence_id", idempotenceID),
		zap.String("base_url", c.config.JD.BaseURL),
		zap.Any("custom_headers", headers),
	)

	// Prepare request
	req := c.client.R().SetContext(ctx)

	// Set headers based on request type
	if formData != nil {
		req.SetHeader("Content-Type", "application/x-www-form-urlencoded")
		req.SetHeader("Accept", "application/x-www-form-urlencoded")
		// Convert map to form data
		formValues := make(map[string]string)
		for k, v := range formData {
			formValues[k] = v
		}
		req.SetFormData(formValues)
		logger.Info("Request form data", zap.Any("form_data", maskFormData(formData)))
	} else {
		req.SetHeader("Content-Type", "application/json")
		req.SetHeader("Accept", "application/json")
		if request != nil {
			req.SetBody(request)
			logger.Info("Request body", zap.Any("body", request))
		}
	}

	// Add custom headers if provided
	if headers != nil {
		for key, value := range headers {
			if value != "" {
				req.SetHeader(key, value)
				logger.Info(fmt.Sprintf("Added custom header, %s=%s", key, value))
			}
		}
	}

	// Add idempotence ID header if provided
	if idempotenceID != "" {
		req.SetHeader("Chave-Idempotencia", idempotenceID)
	}

	// Add authorization header if provided
	if accessToken != "" {
		req.SetHeader("Authorization", fmt.Sprintf("Bearer %s", accessToken))
		logger.Info(fmt.Sprintf("Added authorization header, token_prefix=%s", maskStringBase(accessToken)))
	}

	// Send request based on method
	var resp *resty.Response
	var err error

	switch method {
	case "POST":
		resp, err = req.Post(url)
	case "PUT":
		resp, err = req.Put(url)
	case "GET":
		resp, err = req.Get(url)
	default:
		return nil, fmt.Errorf("unsupported HTTP method: %s", method)
	}

	// Log response details
	if err != nil {
		logger.Error("JD API Request Failed", zap.Error(err))
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	responseBody := string(resp.Body())
	logger.Info("JD API response details",
		zap.Int("status_code", resp.StatusCode()),
		zap.String("status", resp.Status()),
		zap.Int("response_size", len(resp.Body())),
		zap.Duration("response_time", resp.Time()),
		zap.String("body", responseBody),
	)

	return resp, nil
}

// generate IdempotenceID, format: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
func (c *HTTPJDPIClient) GenerateIdempotenceId() string {
	u := uuid.New()
	return hex.EncodeToString(u[:])
}

// maskFormData masks sensitive form data for logging
func maskFormData(formData map[string]string) map[string]string {
	masked := make(map[string]string)
	for k, v := range formData {
		if k == "client_secret" || k == "password" {
			masked[k] = maskStringBase(v)
		} else {
			masked[k] = v
		}
	}
	return masked
}

// maskStringBase masks sensitive string for logging
func maskStringBase(s string) string {
	if len(s) <= 4 {
		return "****"
	}
	return s[:2] + "****" + s[len(s)-2:]
}
