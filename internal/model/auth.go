package model

import (
	"fmt"
	"strings"
	"time"
)

// AuthenticationRequest JD authentication request structure
type AuthenticationRequest struct {
	ClientID     string `json:"client_id" validate:"required"`
	ClientSecret string `json:"client_secret" validate:"required"`
	GrantType    string `json:"grant_type" validate:"required"`
	Scope        string `json:"scope" validate:"required"`
}

// AccessTokenResponse JD access token response structure
type AccessTokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int64  `json:"expires_in"`
	Scope       string `json:"scope"`
}

// AccessToken internal access token structure
type AccessToken struct {
	AccessToken string    `json:"access_token"`
	TokenType   string    `json:"token_type"`
	ExpiresIn   int64     `json:"expires_in"`
	Scope       string    `json:"scope"`
	ExpiresAt   time.Time `json:"expires_at"`
	CreatedAt   time.Time `json:"created_at"`
}

// IsExpired checks if the token is expired
func (t *AccessToken) IsExpired() bool {
	// Check expiration 1 minute in advance to avoid edge cases
	return time.Now().Add(time.Minute).After(t.ExpiresAt)
}

// IsValid checks if the token is valid
func (t *AccessToken) IsValid() bool {
	return t.AccessToken != "" && !t.IsExpired()
}

// NewAccessToken creates a new access token
func NewAccessToken(response *AccessTokenResponse) *AccessToken {
	now := time.Now()
	return &AccessToken{
		AccessToken: response.AccessToken,
		TokenType:   response.TokenType,
		ExpiresIn:   response.ExpiresIn,
		Scope:       response.Scope,
		ExpiresAt:   now.Add(time.Duration(response.ExpiresIn) * time.Second),
		CreatedAt:   now,
	}
}

// JDError JD API error response structure
// Supports multiple error formats from JD API
type JDError struct {
	// Format 1: Authentication errors
	ErrorType        string `json:"error"`
	ErrorDescription string `json:"error_description"`
	ErrorCode        string `json:"error_code"`

	// Format 2: Business logic errors (JDPI specific)
	Codigo       string `json:"codigo"`
	Mensagem     string `json:"mensagem"`
	IdCorrelacao string `json:"idCorrelacao"`

	// Format 3: Validation errors
	Erros []JDValidationError `json:"erros"`
}

// JDValidationError represents field validation errors
type JDValidationError struct {
	Campo     string   `json:"campo"`
	Mensagens []string `json:"mensagens"`
}

// Error implements the error interface
func (e *JDError) Error() string {
	if e.ErrorDescription != "" {
		return e.ErrorDescription
	}
	if e.Mensagem != "" {
		return e.Mensagem
	}
	if e.ErrorType != "" {
		return e.ErrorType
	}
	if len(e.Erros) > 0 {
		return "Validation errors occurred"
	}
	return "Unknown error"
}

// GetDetailedErrorMessage returns a detailed error message for logging and response details
func (e *JDError) GetDetailedErrorMessage() string {
	if e.ErrorDescription != "" || e.ErrorType != "" {
		// Format 1: Authentication errors
		if e.ErrorDescription != "" {
			return fmt.Sprintf("Error: %s, Description: %s, Code: %s", e.ErrorType, e.ErrorDescription, e.ErrorCode)
		}
		return fmt.Sprintf("Error: %s, Code: %s", e.ErrorType, e.ErrorCode)
	}

	if e.Codigo != "" && e.Mensagem != "" {
		// Format 2: Business logic errors
		if len(e.Erros) > 0 {
			// Format 3: Validation errors combined with business errors
			var validationDetails []string
			for _, erro := range e.Erros {
				for _, msg := range erro.Mensagens {
					validationDetails = append(validationDetails, fmt.Sprintf("%s: %s", erro.Campo, msg))
				}
			}
			correlationInfo := ""
			if e.IdCorrelacao != "" {
				correlationInfo = fmt.Sprintf(", Correlation ID: %s", e.IdCorrelacao)
			}
			return fmt.Sprintf("Code: %s, Message: %s%s, Validation Errors: [%s]",
				e.Codigo, e.Mensagem, correlationInfo, strings.Join(validationDetails, "; "))
		}
		correlationInfo := ""
		if e.IdCorrelacao != "" {
			correlationInfo = fmt.Sprintf(", Correlation ID: %s", e.IdCorrelacao)
		}
		return fmt.Sprintf("Code: %s, Message: %s%s", e.Codigo, e.Mensagem, correlationInfo)
	}

	if len(e.Erros) > 0 {
		// Format 3: Pure validation errors
		var validationDetails []string
		for _, erro := range e.Erros {
			for _, msg := range erro.Mensagens {
				validationDetails = append(validationDetails, fmt.Sprintf("%s: %s", erro.Campo, msg))
			}
		}
		return fmt.Sprintf("Validation Errors: [%s]", strings.Join(validationDetails, "; "))
	}

	return "Unknown error format"
}
