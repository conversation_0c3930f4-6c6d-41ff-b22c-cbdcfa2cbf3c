package model

import "time"

// PixPaymentJDHeaders represents the JD API headers required for PixPayment operations
type PixPaymentJDHeaders struct {
	IdempotenceID string // Chave-Idempotencia header
	EndToEndID    string // PI-EndToEndId header (optional for some operations)
}

// PixOutConfirmJDRequest represents the JD API request for confirming PIX payment
type PixOutConfirmJDRequest struct {
	ClientRequestID       string              `json:"idReqSistemaCliente" validate:"required"`
	RequestTime           *time.Time          `json:"dtHrRequisicaoPsp,omitempty"`
	InitiationType        int                 `json:"tpIniciacao" validate:"required"`
	LiquidationPriority   int                 `json:"prioridadePagamento" validate:"required"`
	PaymentPriority       int                 `json:"tpPrioridadePagamento" validate:"required"`
	Purpose               int                 `json:"finalidade" validate:"required"`
	AgentModality         int                 `json:"modalidadeAgente,omitempty"`
	PssIspb               int                 `json:"ispbPss,omitempty"`
	Payer                 PixParticipantJD    `json:"pagador" validate:"required"`
	Payee                 PixParticipantJD    `json:"recebedor" validate:"required"`
	Amount                float64             `json:"valor" validate:"required,gt=0"`
	AmountDetails         []PixAmountDetailJD `json:"vlrDetalhe,omitempty"`
	PixKey                string              `json:"chave,omitempty"`
	EndToEndID            string              `json:"endToEndId" validate:"required"`
	PayeeReconciliationID string              `json:"idConciliacaoRecebedor,omitempty"`
	ClientInfo            string              `json:"infEntreClientes,omitempty"`
}

// PixParticipantJD represents payer or payee information in JD API format
type PixParticipantJD struct {
	Ispb          int    `json:"ispb" validate:"required"`
	PersonType    int    `json:"tpPessoa" validate:"required"`
	CpfCnpj       int64  `json:"cpfCnpj" validate:"required"`
	Name          string `json:"nome" validate:"required"`
	AgencyNumber  string `json:"nrAgencia" validate:"required"`
	AccountType   int    `json:"tpConta" validate:"required"`
	AccountNumber string `json:"nrConta" validate:"required"`
}

// PixAmountDetailJD represents amount detail information in JD API format
type PixAmountDetailJD struct {
	CashFeeAmount float64 `json:"vlrTarifaDinheiroCompra,omitempty"`
	AmountType    int     `json:"tipo" validate:"required"`
}

// PixOutConfirmJDResponse represents the JD API response for confirming PIX payment
type PixOutConfirmJDResponse struct {
	ClientRequestID string    `json:"idReqSistemaCliente"`
	JDRequestID     string    `json:"idReqJdPi"`
	EndToEndID      string    `json:"endToEndId"`
	JDRequestTime   time.Time `json:"dtHrReqJdPi"`
}

// PixTransactionGetJDRequest represents the JD API request for getting transaction by EndToEndId
type PixTransactionGetJDRequest struct {
	JDRequestID string `json:"idReqJdPiConsultada" validate:"required"`
}

// PixTransactionGetJDResponse represents the JD API response for getting transaction by EndToEndId
type PixTransactionGetJDResponse struct {
	JDRequestID           string              `json:"idReqJdPiConsultada"`
	JDRequestTime         time.Time           `json:"dtHrReqJdPi"`
	StatusTime            time.Time           `json:"dtHrSituacao"`
	IntermediateStatus    int                 `json:"stJdPiProc"`
	FinalStatus           int                 `json:"stJdPi"`
	ErrorCode             *string             `json:"codigoErro,omitempty"`
	ErrorDescription      *string             `json:"descCodigoErro,omitempty"`
	EndToEndID            string              `json:"endToEndId"`
	ExecutionTime         time.Time           `json:"dtHrEfetivacao"`
	InitiationType        int                 `json:"tpIniciacao"`
	LiquidationPriority   int                 `json:"prioridadePagamento"`
	PaymentPriority       int                 `json:"tpPrioridadePagamento"`
	Purpose               int                 `json:"finalidade"`
	AgentModality         *int                `json:"modalidadeAgente,omitempty"`
	InitiatorCNPJ         int64               `json:"cnpjIniciadorPagamento"`
	Payer                 PixParticipantJD    `json:"pagador"`
	Payee                 PixParticipantJD    `json:"recebedor"`
	Amount                float64             `json:"valor"`
	AmountDetails         []PixAmountDetailJD `json:"vlrDetalhe,omitempty"`
	PixKey                *string             `json:"chave,omitempty"`
	PayeeReconciliationID *string             `json:"idConciliacaoRecebedor,omitempty"`
	ClientInfo            *string             `json:"infEntreClientes,omitempty"`
}
