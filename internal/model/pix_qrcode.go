package model

import "time"

// QRCodeJDHeaders represents the JD API headers required for QRCode operations
type QRCodeJDHeaders struct {
	IdempotenceID string // Chave-Idempotencia header
	EndToEndID    string // PI-EndToEndId header
	PayerID       string // PI-PayerId header
}

// QRCodeStaticCreateJDRequest JD API request for creating static QR code
type QRCodeStaticCreateJDRequest struct {
	Format                   int     `json:"formato" validate:"required"`         // TipoFormato
	PixKey                   string  `json:"chave" validate:"required"`           // PIX key
	CategoryCode             string  `json:"codigoCategoria" validate:"required"` // Category code
	Amount                   float64 `json:"valor,omitempty"`                     // Amount (optional for static)
	ReceiverName             string  `json:"nomeRecebedor" validate:"required"`   // Receiver name
	City                     string  `json:"cidade" validate:"required"`          // City
	PostalCode               string  `json:"cep,omitempty"`                       // Postal code (optional)
	ReceiverReconciliationID string  `json:"idConciliacaoRecebedor,omitempty"`    // Receiver reconciliation ID (optional)
	AdditionalData           string  `json:"dadosAdicionais,omitempty"`           // Additional data (optional)
	IspbFss                  int     `json:"ispbFss,omitempty"`                   // ISPB FSS (optional)
}

// QRCodeStaticCreateJDResponse JD API response for creating static QR code
type QRCodeStaticCreateJDResponse struct {
	QRCodeImageBase64   string `json:"imagemQrCodeInBase64"`
	QRCodePayloadBase64 string `json:"payloadBase64"`
}

// QRCodeDynamicCreateJDRequest JD API request for creating dynamic QR code
type QRCodeDynamicCreateJDRequest struct {
	Ispb                     string             `json:"ispb" validate:"required"`
	Format                   int                `json:"formato" validate:"required"`
	PixKey                   string             `json:"chave" validate:"required"`
	CategoryCode             string             `json:"codigoCategoria" validate:"required"`
	ReceiverName             string             `json:"nomeRecebedor" validate:"required"`
	PayerRequest             string             `json:"solicitacaoPagador,omitempty"`
	PayerCPF                 string             `json:"cpfPagador,omitempty"`
	PayerCNPJ                string             `json:"cnpjPagador,omitempty"`
	PayerName                string             `json:"nomePagador,omitempty"`
	City                     string             `json:"cidade" validate:"required"`
	PostalCode               string             `json:"cep" validate:"required"`
	OriginalAmount           float64            `json:"valorOriginal" validate:"required"`
	ChangeModality           int                `json:"modalidadeAlteracao" validate:"required"`
	WithdrawAmount           float64            `json:"valorSaque,omitempty"`
	WithdrawChangeModality   int                `json:"modalidadeAltSaque,omitempty"`
	WithdrawIspbPss          int                `json:"ispbPssSaque,omitempty"`
	WithdrawAgentModality    int                `json:"modalidadeAgSaque,omitempty"`
	ChangeAmount             float64            `json:"valorTroco,omitempty"`
	ChangeChangeModality     int                `json:"modalidadeAltTroco,omitempty"`
	ChangeIspbPss            int                `json:"ispbPssTroco,omitempty"`
	ChangeAgentModality      int                `json:"modalidadeAgtroco,omitempty"`
	QRExpiration             int                `json:"expiracaoQR,omitempty"`
	ReceiverReconciliationID string             `json:"idConciliacaoRecebedor" validate:"required"`
	AdditionalData           []AdditionalDataJD `json:"dadosAdicionais,omitempty"`
	Reusable                 bool               `json:"reutilizavel"`
	PayloadJSONURL           string             `json:"urlPayloadJson" validate:"required"`
	JWKURL                   string             `json:"urlJwk" validate:"required"`
}

// QRCodeDynamicCreateJDResponse JD API response for creating dynamic QR code
type QRCodeDynamicCreateJDResponse struct {
	DocumentID          string `json:"idDocumento"`
	QRCodeImageBase64   string `json:"imagemQrCodeInBase64"`
	QRCodePayloadBase64 string `json:"payloadBase64"`
	PayloadJWS          string `json:"payloadJws"`
	Revision            int    `json:"revisao"`
}

// QRCodeGetByPayloadJDRequest JD API request for getting QR code by payload
type QRCodeGetByPayloadJDRequest struct {
	QRCodePayload string `json:"qrCodePayload" validate:"required"`
}

// QRCodeGetByPayloadJDResponse JD API response for getting QR code by payload
type QRCodeGetByPayloadJDResponse struct {
	EndToEndID               string                      `json:"EndToEndId"`
	QRCodeType               int                         `json:"TpQRCode"`
	StaticQRCodeData         *StaticQRCodeDataJD         `json:"DadosQrCodeEstatico,omitempty"`
	DynamicQRCodeData        *DynamicQRCodeDataJD        `json:"DadosQrCodeDinamico,omitempty"`
	DynamicQRCodeDataWithDue *DynamicQRCodeDataWithDueJD `json:"DadosQrCodeDinamicoCobv,omitempty"`
}

// StaticQRCodeDataJD JD API DTO for static QR code data
type StaticQRCodeDataJD struct {
	Amount                   float64 `json:"Valor"`
	AdditionalData           string  `json:"DadosAdicionais"`
	Ispb                     int     `json:"Ispb"`
	PixKey                   string  `json:"Chave"`
	Branch                   string  `json:"NrAgencia"`
	AccountType              int     `json:"TpConta"`
	Account                  string  `json:"NrConta"`
	CategoryCode             string  `json:"CodigoCategoria"`
	ReceiverName             string  `json:"NomeRecebedor"`
	ReceiverPersonType       int     `json:"TpPessoaRecebedor"`
	ReceiverCPFCNPJ          int64   `json:"CpfCnpjRecebedor"`
	City                     string  `json:"Cidade"`
	ReceiverReconciliationID string  `json:"IdConciliacaoRecebedor"`
}

// DynamicQRCodeDataJD JD API DTO for dynamic QR code data
type DynamicQRCodeDataJD struct {
	Revision                 int                `json:"Revisao"`
	Ispb                     int                `json:"Ispb"`
	Branch                   string             `json:"NrAgencia"`
	AccountType              int                `json:"TpConta"`
	Account                  string             `json:"NrConta"`
	PixKey                   string             `json:"Chave"`
	ReceiverReconciliationID string             `json:"IdConciliacaoRecebedor"`
	CategoryCode             string             `json:"CodigoCategoria"`
	ReceiverName             string             `json:"NomeRecebedor"`
	ReceiverPersonType       int                `json:"TpPessoaRecebedor"`
	ReceiverCPFCNPJ          int64              `json:"CpfCnpjRecebedor"`
	PayerRequest             string             `json:"SolicitacaoPagador"`
	PayerCNPJ                string             `json:"CnpjPagador"`
	PayerName                string             `json:"NomePagador"`
	City                     string             `json:"Cidade"`
	PostalCode               string             `json:"Cep"`
	OriginalAmount           float64            `json:"ValorOriginal"`
	QRExpiration             int                `json:"ExpiracaoQR"`
	AdditionalData           []AdditionalDataJD `json:"DadosAdicionais,omitempty"`
	CreationDateTime         time.Time          `json:"DtHrCriacao"`
	PresentationDateTime     time.Time          `json:"DtHrApresentacao"`
	ReceiverPSPURL           string             `json:"UrlPSPRecebedor"`
	Reusable                 bool               `json:"Reutilizavel"`
	Status                   int                `json:"Status"`
}

// DynamicQRCodeDataWithDueJD JD API DTO for dynamic QR code data with due date
type DynamicQRCodeDataWithDueJD struct {
	Revision                 int                `json:"Revisao"`
	Ispb                     int                `json:"Ispb"`
	Branch                   string             `json:"NrAgencia"`
	AccountType              int                `json:"TpConta"`
	Account                  string             `json:"NrConta"`
	PixKey                   string             `json:"Chave"`
	CategoryCode             string             `json:"CodigoCategoria"`
	ReceiverPersonType       int                `json:"TpPessoaRecebedor"`
	ReceiverCPFCNPJ          int64              `json:"CpfCnpjRecebedor"`
	ReceiverName             string             `json:"NomeRecebedor"`
	ReceiverAddress          string             `json:"LogradouroRecebedor"`
	City                     string             `json:"Cidade"`
	State                    string             `json:"Uf"`
	PostalCode               string             `json:"Cep"`
	PayerRequest             string             `json:"SolicitacaoPagador"`
	PayerCNPJ                string             `json:"CnpjPagador"`
	PayerName                string             `json:"NomePagador"`
	OriginalAmount           float64            `json:"ValorOriginal"`
	Discount                 float64            `json:"Abatimento"`
	Rebate                   float64            `json:"Desconto"`
	Interest                 float64            `json:"Juros"`
	Fine                     float64            `json:"Multa"`
	FinalAmount              float64            `json:"ValorFinal"`
	DueDate                  time.Time          `json:"DtVenc"`
	DaysAfterDue             int                `json:"DiasAposVenc"`
	ReceiverReconciliationID string             `json:"IdConciliacaoRecebedor"`
	AdditionalData           []AdditionalDataJD `json:"DadosAdicionais,omitempty"`
	CreationDateTime         time.Time          `json:"DtHrCriacao"`
	PresentationDateTime     time.Time          `json:"DtHrApresentacao"`
	ReceiverPSPURL           string             `json:"UrlPSPRecebedor"`
	Reusable                 bool               `json:"Reutilizavel"`
	Status                   int                `json:"Status"`
}

// AdditionalDataJD JD API DTO for additional data
type AdditionalDataJD struct {
	Name  string `json:"Nome"`
	Value string `json:"Valor"`
}

// QRCodeDynamicDecodeByUrlJDRequest JD API request for decoding dynamic QR code by URL
type QRCodeDynamicDecodeByUrlJDRequest struct {
	URLPayloadJSON   string `json:"urlPayloadJson" validate:"required"` // URL do payload JSON
	CityCode         string `json:"codMun,omitempty"`                   // Código do município (opcional)
	PresentationDate string `json:"dPP,omitempty"`                      // Data de apresentação (formato YYYY-MM-DD, opcional)
}

// QRCodeDynamicDecodeByUrlJDResponse JD API response for decoding dynamic QR code by URL
// Note: This API only returns dynamic QR code data, no static QR code data
type QRCodeDynamicDecodeByUrlJDResponse struct {
	EndToEndID               string                      `json:"EndToEndId"`
	QRCodeType               int                         `json:"TpQRCode"`
	DynamicQRCodeData        *DynamicQRCodeDataJD        `json:"DadosQrCodeDinamico,omitempty"`
	DynamicQRCodeDataWithDue *DynamicQRCodeDataWithDueJD `json:"DadosQrCodeDinamicoCobv,omitempty"`
}
