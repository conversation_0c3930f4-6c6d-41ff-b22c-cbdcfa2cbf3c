package mock

import (
	"context"

	"jdpi-gateway/internal/model"

	"go.uber.org/zap"
)

// MockPixQRCodeClient is a mock implementation of PixQRCodeClient for testing
type MockPixQRCodeClient struct {
	logger *zap.Logger
}

// NewMockPixQRCodeClient creates a new mock PIX QRCode client
func NewMockPixQRCodeClient(logger *zap.Logger) *MockPixQRCodeClient {
	return &MockPixQRCodeClient{
		logger: logger,
	}
}

// CreateStaticQRCode mock implementation for static QR code creation
func (m *MockPixQRCodeClient) CreateStaticQRCode(ctx context.Context, req *model.QRCodeStaticCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeStaticCreateJDResponse, error) {
	m.logger.Info("Mock: CreateStaticQRCode called",
		zap.String("pix_key", req.PixKey),
		zap.Float64("amount", req.Amount),
		zap.Any("headers", headers))

	// Return mock response
	return &model.QRCodeStaticCreateJDResponse{
		QRCodeImageBase64:   "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
		QRCodePayloadBase64: "MDAwMjAxMjY1ODAwMTRCUi5HT1YuQkNCLlBJWA==",
	}, nil
}

// CreateDynamicQRCode mock implementation for dynamic QR code creation
func (m *MockPixQRCodeClient) CreateDynamicQRCode(ctx context.Context, req *model.QRCodeDynamicCreateJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicCreateJDResponse, error) {
	m.logger.Info("Mock: CreateDynamicQRCode called",
		zap.String("pix_key", req.PixKey),
		zap.Any("headers", headers))

	// Return mock response
	return &model.QRCodeDynamicCreateJDResponse{
		DocumentID:          "mock-doc-12345",
		QRCodeImageBase64:   "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
		QRCodePayloadBase64: "MDAwMjAxMjY1ODAwMTRCUi5HT1YuQkNCLlBJWA==",
		PayloadJWS:          "eyJhbGciOiJSUzI1NiJ9.eyJpc3MiOiJNT0NLIn0.mock-signature",
		Revision:            1,
	}, nil
}

// DecodeQRCodeByPayload mock implementation for getting QR code by payload
func (m *MockPixQRCodeClient) DecodeQRCodeByPayload(ctx context.Context, req *model.QRCodeGetByPayloadJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeGetByPayloadJDResponse, error) {
	m.logger.Info("Mock: DecodeQRCodeByPayload called",
		zap.String("qrcode_payload", req.QRCodePayload),
		zap.Any("headers", headers))

	// Return mock response
	return &model.QRCodeGetByPayloadJDResponse{
		EndToEndID: "E12345678901234567890123456789012",
		QRCodeType: 2, // Both image and payload
		StaticQRCodeData: &model.StaticQRCodeDataJD{
			Amount:                   100.50,
			AdditionalData:           "Mock QR Code",
			Ispb:                     48756121,
			PixKey:                   "<EMAIL>",
			CategoryCode:             "0014",
			ReceiverName:             "Mock User",
			ReceiverPersonType:       1,
			ReceiverCPFCNPJ:          12345678901,
			City:                     "Mock City",
			ReceiverReconciliationID: "mock-tx-id",
		},
	}, nil
}

// DecodeDynamicQRCodeByUrl mock implementation for decoding dynamic QR code by URL
func (m *MockPixQRCodeClient) DecodeDynamicQRCodeByUrl(ctx context.Context, req *model.QRCodeDynamicDecodeByUrlJDRequest, headers *model.QRCodeJDHeaders) (*model.QRCodeDynamicDecodeByUrlJDResponse, error) {
	m.logger.Info("Mock: DecodeDynamicQRCodeByUrl called",
		zap.String("url_payload_json", req.URLPayloadJSON),
		zap.Any("headers", headers))

	// Return mock response
	return &model.QRCodeDynamicDecodeByUrlJDResponse{
		EndToEndID: "E12345678901234567890123456789012",
		QRCodeType: 1, // Dynamic instant payment
		DynamicQRCodeData: &model.DynamicQRCodeDataJD{
			Revision:                 1,
			Ispb:                     48756121,
			PixKey:                   "<EMAIL>",
			ReceiverReconciliationID: "mock-tx-id",
			CategoryCode:             "0014",
			ReceiverName:             "Mock User",
			ReceiverPersonType:       1,
			ReceiverCPFCNPJ:          12345678901,
			City:                     "Mock City",
			PostalCode:               "01234567",
			OriginalAmount:           100.50,
			QRExpiration:             3600,
			Reusable:                 false,
			Status:                   1,
		},
	}, nil
}
