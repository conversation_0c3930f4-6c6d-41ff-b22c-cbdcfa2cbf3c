package mock

import (
	"context"
	"time"

	"jdpi-gateway/internal/model"

	"go.uber.org/zap"
)

// MockPixPaymentClient is a mock implementation of PixPaymentClient for testing
type MockPixPaymentClient struct {
	logger *zap.Logger
}

// NewMockPixPaymentClient creates a new mock PIX Payment client
func NewMockPixPaymentClient(logger *zap.Logger) *MockPixPaymentClient {
	return &MockPixPaymentClient{
		logger: logger,
	}
}

// PixOutConfirm mock implementation for PIX payment confirmation
func (m *MockPixPaymentClient) PixOutConfirm(ctx context.Context, req *model.PixOutConfirmJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixOutConfirmJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	m.logger.Info("Mock: PixOutConfirm called",
		zap.String("client_request_id", req.ClientRequestID),
		zap.String("end_to_end_id", req.EndToEndID),
		zap.Float64("amount", req.Amount),
		zap.String("idempotence_id", idempotenceID),
		zap.Any("headers", headers))

	// Return mock response
	return &model.PixOutConfirmJDResponse{
		ClientRequestID: req.ClientRequestID,
		JDRequestID:     "mock-jd-req-12345",
		EndToEndID:      req.EndToEndID,
		JDRequestTime:   time.Now(),
	}, nil
}

// PixTransactionGet mock implementation for getting PIX transaction status
func (m *MockPixPaymentClient) PixTransactionGet(ctx context.Context, req *model.PixTransactionGetJDRequest, headers *model.PixPaymentJDHeaders) (*model.PixTransactionGetJDResponse, error) {
	idempotenceID := ""
	if headers != nil {
		idempotenceID = headers.IdempotenceID
	}
	m.logger.Info("Mock: PixTransactionGet called",
		zap.String("end_to_end_id", req.JDRequestID),
		zap.String("idempotence_id", idempotenceID),
		zap.Any("headers", headers))

	// Return mock response
	now := time.Now()
	return &model.PixTransactionGetJDResponse{
		JDRequestID:         "mock-jd-req-12345",
		JDRequestTime:       now.Add(-5 * time.Minute),
		StatusTime:          now,
		IntermediateStatus:  0, // SUCCESS
		FinalStatus:         9, // TRANSACTION_SUCCESS
		ErrorCode:           nil,
		ErrorDescription:    nil,
		EndToEndID:          req.JDRequestID,
		ExecutionTime:       now.Add(-2 * time.Minute),
		InitiationType:      1, // PIX_KEY
		LiquidationPriority: 1, // NON_PRIORITY_LIQUIDATION
		PaymentPriority:     0, // PRIORITY_PAYMENT
		Purpose:             0, // PURCHASE_OR_TRANSFER
		AgentModality:       nil,
		InitiatorCNPJ:       ********000190,
		Payer: model.PixParticipantJD{
			Ispb:          ********,
			PersonType:    0, // NATURAL
			CpfCnpj:       ********901,
			Name:          "Mock Payer",
			AgencyNumber:  "0001",
			AccountType:   0, // CHECKING_ACCOUNT
			AccountNumber: "********9",
		},
		Payee: model.PixParticipantJD{
			Ispb:          ********,
			PersonType:    0, // NATURAL
			CpfCnpj:       *********00,
			Name:          "Mock Payee",
			AgencyNumber:  "0001",
			AccountType:   0, // CHECKING_ACCOUNT
			AccountNumber: "*********",
		},
		Amount:                100.50,
		AmountDetails:         []model.PixAmountDetailJD{},
		PixKey:                stringPtr("<EMAIL>"),
		PayeeReconciliationID: stringPtr("mock-reconciliation-id"),
		ClientInfo:            stringPtr("Mock client info"),
	}, nil
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}
