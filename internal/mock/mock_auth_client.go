package mock

import (
	"context"

	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"

	"go.uber.org/zap"
)

// MockAuthClient Mock implementation of authentication client
type MockAuthClient struct {
	logger *zap.Logger
}

// NewMockAuthClient creates a new mock authentication client
func NewMockAuthClient(logger *zap.Logger) *MockAuthClient {
	return &MockAuthClient{
		logger: logger,
	}
}

// GetAccessToken mock implementation
func (c *MockAuthClient) GetAccessToken(ctx context.Context) (*model.AccessToken, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(zap.String("trace_id", traceID))

	logger.Info("Mock: Getting access token")

	mockResponse := &model.AccessTokenResponse{
		AccessToken: "mock_access_token_12345",
		TokenType:   "Bearer",
		ExpiresIn:   3600,
		Scope:       "jdpi",
	}

	return model.NewAccessToken(mockResponse), nil
}
