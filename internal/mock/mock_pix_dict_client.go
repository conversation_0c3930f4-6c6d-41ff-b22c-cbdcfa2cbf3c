package mock

import (
	"context"
	"fmt"
	"time"

	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"

	"go.uber.org/zap"
)

// MockPixDictClient Mock implementation of PIX Dict client
type MockPixDictClient struct {
	logger *zap.Logger
}

// NewMockPixDictClient creates a new mock PIX Dict client
func NewMockPixDictClient(logger *zap.Logger) *MockPixDictClient {
	return &MockPixDictClient{
		logger: logger,
	}
}

// CreatePixKey mock implementation
func (c *MockPixDictClient) CreatePixKey(ctx context.Context, req *model.CreatePixKeyJDRequest, idempotenceID string) (*model.CreatePixKeyJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
	)

	logger.Info("Mock: Creating PIX key", zap.String("pix_key", req.<PERSON>x<PERSON><PERSON>))

	return &model.CreatePixKeyJDResponse{
		PixKey:                       req.Pix<PERSON>,
		PixKeyCreationDatetime:       "2024-01-01T10:00:00Z",
		PixKeyOwnershipStartDatetime: "2024-01-01T10:00:00Z",
		ClaimOpeningDatetime:         "2024-01-01T10:00:00Z",
	}, nil
}

// UpdatePixKey mock implementation
func (c *MockPixDictClient) UpdatePixKey(ctx context.Context, req *model.UpdatePixKeyJDRequest, idempotenceID string) (*model.UpdatePixKeyJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
	)

	logger.Info("Mock: Updating PIX key", zap.String("pix_key", req.PixKey))

	return &model.UpdatePixKeyJDResponse{
		PixKey:                       req.PixKey,
		PixKeyCreationDatetime:       "2024-01-01T10:00:00Z",
		PixKeyOwnershipStartDatetime: "2024-01-01T10:00:00Z",
		ClaimOpeningDatetime:         "2024-01-01T10:00:00Z",
	}, nil
}

// DeletePixKey mock implementation
func (c *MockPixDictClient) DeletePixKey(ctx context.Context, req *model.DeletePixKeyJDRequest, idempotenceID string) (*model.DeletePixKeyJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
	)

	logger.Info("Mock: Deleting PIX key", zap.String("pix_key", req.PixKey))

	return &model.DeletePixKeyJDResponse{
		PixKey: req.PixKey,
	}, nil
}

// NotifyAccountClosure mock implementation
func (c *MockPixDictClient) NotifyAccountClosure(ctx context.Context, req *model.NotifyAccountClosureJDRequest, idempotenceID string) (*model.NotifyAccountClosureJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
	)

	logger.Info("Mock: Notifying account closure")

	return &model.NotifyAccountClosureJDResponse{
		ProcessingDatetime: "2024-01-01T10:00:00Z",
	}, nil
}

// NotifyOwnershipLinkClosure mock implementation
func (c *MockPixDictClient) NotifyOwnershipLinkClosure(ctx context.Context, req *model.NotifyOwnershipLinkClosureJDRequest, idempotenceID string) (*model.NotifyOwnershipLinkClosureJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
	)

	logger.Info("Mock: Notifying ownership link closure")

	return &model.NotifyOwnershipLinkClosureJDResponse{
		ProcessingDatetime: "2024-01-01T10:00:00Z",
	}, nil
}

// ListPixKeys mock implementation
func (c *MockPixDictClient) ListPixKeys(ctx context.Context, req *model.ListPixKeysJDRequest, idempotenceID string) (*model.ListPixKeysJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
	)

	logger.Info("Mock: Listing PIX keys")

	return &model.ListPixKeysJDResponse{
		JDApiReturnDatetime: "2024-01-01T10:00:00Z",
		PixKeys: []model.AssociatedPixKeyJDDto{
			{
				PixKeyType:                   4, // EVP
				PixKey:                       "mock-pix-key-1",
				Name:                         "Mock User 1",
				PixKeyCreationDatetime:       "2024-01-01T10:00:00Z",
				PixKeyOwnershipStartDatetime: "2024-01-01T10:00:00Z",
			},
			{
				PixKeyType:                   2, // EMAIL
				PixKey:                       "<EMAIL>",
				Name:                         "Mock User 2",
				PixKeyCreationDatetime:       "2024-01-01T11:00:00Z",
				PixKeyOwnershipStartDatetime: "2024-01-01T11:00:00Z",
			},
			{
				PixKeyType:                   0, // CPF
				PixKey:                       "***********",
				Name:                         "Mock User 3",
				PixKeyCreationDatetime:       "2024-01-01T12:00:00Z",
				PixKeyOwnershipStartDatetime: "2024-01-01T12:00:00Z",
			},
		},
	}, nil
}

// QueryPixKey mock implementation
func (c *MockPixDictClient) QueryPixKey(ctx context.Context, req *model.QueryPixKeyJDRequest, idempotenceID string) (*model.QueryPixKeyJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
	)

	logger.Info("Mock: Querying PIX key", zap.String("pix_key", req.PixKey))

	return &model.QueryPixKeyJDResponse{
		PixKeyType:                   1,
		PixKey:                       req.PixKey,
		Ispb:                         12345,
		Branch:                       "0001",
		AccountType:                  1,
		Account:                      "123456",
		AccountOpeningDatetime:       "2024-01-01T10:00:00Z",
		PersonType:                   1,
		Document:                     ***********,
		Name:                         "Mock User",
		TradeName:                    "Mock Company",
		PixKeyCreationDatetime:       "2024-01-01T10:00:00Z",
		PixKeyOwnershipStartDatetime: "2024-01-01T10:00:00Z",
		ClaimOpeningDatetime:         "2024-01-01T10:00:00Z",
		EndToEndId:                   req.EndToEndId,
		Statistics: model.StatisticsDto{
			LastAntiFraudUpdateDatetime: "2024-01-01T10:00:00Z",
			Counters: []model.CounterDto{
				{Type: 1, Aggregated: 10, D3: 3, D30: 30, M6: 180},
			},
		},
	}, nil
}

// VerifyPixKeyExistence mock implementation
func (c *MockPixDictClient) VerifyPixKeyExistence(ctx context.Context, req *model.VerifyPixKeyExistenceJDRequest, idempotenceID string) (*model.VerifyPixKeyExistenceJDResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := c.logger.With(
		zap.String("trace_id", traceID),
		zap.String("idempotence_id", idempotenceID),
	)

	logger.Info("Mock: Verifying PIX key existence")

	verifiedPixKeys := make([]model.VerifiedPixKeyJDDto, len(req.PixKeysToVerify))
	for i, pixKey := range req.PixKeysToVerify {
		verifiedPixKeys[i] = model.VerifiedPixKeyJDDto{
			PixKey:       pixKey.PixKey,
			ExistsInDict: true, // Mock: assume all PIX keys exist
		}
	}

	return &model.VerifyPixKeyExistenceJDResponse{
		DictReturnDatetime: "2024-01-01T10:00:00Z",
		CorrelationId:      fmt.Sprintf("mock-correlation-%d", time.Now().Unix()),
		VerifiedPixKeys:    verifiedPixKeys,
	}, nil
}
