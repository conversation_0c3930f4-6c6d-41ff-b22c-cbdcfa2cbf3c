package handler

import (
	"context"

	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/service"
	"jdpi-gateway/internal/validator"

	"go.uber.org/zap"

	// External protocol imports
	pixservice "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix"
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
)

// PixDictHandler PIX Dict gRPC handler
type PixDictHandler struct {
	pixservice.UnimplementedPixDictServiceServer
	pixDictService service.PixDictService
	validator      *validator.PixDictValidator
	logger         *zap.Logger
}

// NewPixDictHandler creates a new PIX Dict handler
func NewPixDictHandler(pixDictService service.PixDictService, logger *zap.Logger) *PixDictHandler {
	return &PixDictHandler{
		pixDictService: pixDictService,
		validator:      validator.NewPixDictValidator(),
		logger:         logger,
	}
}

// PixKeyCreate creates a PIX key using external protocol
func (h *PixDictHandler) PixKeyCreate(ctx context.Context, req *pixrequest.PixKeyCreateRequest) (*pixresponse.PixKeyCreateResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	logger.Info("Processing CreatePixKey request",
		zap.String("pix_key", req.PixKey.GetKeyValue()),
		zap.String("pix_key_type", req.PixKey.GetKeyType().String()),
	)

	// Call service layer
	response, err := h.pixDictService.CreatePixKey(ctx, req)
	if err != nil {
		logger.Error("Failed to create PIX key",
			zap.String("pix_key", req.PixKey.GetKeyValue()),
			zap.Error(err),
		)
		// Create error response for create operation
		return &pixresponse.PixKeyCreateResponse{
			Result: &pixresponse.PixKeyCreateResponse_Error{
				Error: &pixresponse.Error{
					Error:        pixresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: err.Error(),
				},
			},
		}, nil
	}

	logger.Info("Successfully processed CreatePixKey request",
		zap.String("pix_key", req.PixKey.GetKeyValue()),
	)

	return response, nil
}

// PixKeyDelete deletes a PIX key using external protocol
func (h *PixDictHandler) PixKeyDelete(ctx context.Context, req *pixrequest.PixKeyDeleteRequest) (*pixresponse.PixKeyDeleteResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	logger.Info("Processing DeletePixKey request",
		zap.String("pix_key", req.PixKey.GetKeyValue()),
	)

	// Call service layer
	response, err := h.pixDictService.DeletePixKey(ctx, req)
	if err != nil {
		logger.Error("Failed to delete PIX key",
			zap.String("pix_key", req.PixKey.GetKeyValue()),
			zap.Error(err),
		)
		// Create error response for delete operation
		return &pixresponse.PixKeyDeleteResponse{
			Result: &pixresponse.PixKeyDeleteResponse_Error{
				Error: &pixresponse.Error{
					Error:        pixresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: err.Error(),
				},
			},
		}, nil
	}

	logger.Info("Successfully processed DeletePixKey request",
		zap.String("pix_key", req.PixKey.GetKeyValue()),
	)

	return response, nil
}

// PixKeyUpdate updates a PIX key using external protocol
func (h *PixDictHandler) PixKeyUpdate(ctx context.Context, req *pixrequest.PixKeyUpdateRequest) (*pixresponse.PixKeyUpdateResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	logger.Info("Processing UpdatePixKey request",
		zap.String("pix_key", req.PixKey.GetKeyValue()),
	)

	// Call service layer
	response, err := h.pixDictService.UpdatePixKey(ctx, req)
	if err != nil {
		logger.Error("Failed to update PIX key",
			zap.String("pix_key", req.PixKey.GetKeyValue()),
			zap.Error(err),
		)
		// Create error response for update operation
		return &pixresponse.PixKeyUpdateResponse{
			Result: &pixresponse.PixKeyUpdateResponse_Error{
				Error: &pixresponse.Error{
					Error:        pixresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: err.Error(),
				},
			},
		}, nil
	}

	logger.Info("Successfully processed UpdatePixKey request",
		zap.String("pix_key", req.PixKey.GetKeyValue()),
	)

	return response, nil
}

// PixKeyListByAccount lists PIX keys by account using external protocol
func (h *PixDictHandler) PixKeyListByAccount(ctx context.Context, req *pixrequest.PixKeyListByAccountRequest) (*pixresponse.PixKeyListByAccountResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Get account number from BankAccount
	accountNumber := ""
	if req.BankAccount != nil {
		accountNumber = req.BankAccount.GetAccountNumber()
	}

	logger.Info("Processing ListPixKeys request",
		zap.String("account", accountNumber),
	)

	// Call service layer
	response, err := h.pixDictService.ListPixKeys(ctx, req)
	if err != nil {
		logger.Error("Failed to list PIX keys by account",
			zap.String("account", accountNumber),
			zap.Error(err),
		)
		// Create error response for list operation
		return &pixresponse.PixKeyListByAccountResponse{
			Result: &pixresponse.PixKeyListByAccountResponse_Error{
				Error: &pixresponse.Error{
					Error:        pixresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: err.Error(),
				},
			},
		}, nil
	}

	logger.Info("Successfully processed ListPixKeys request",
		zap.String("account", accountNumber),
	)

	return response, nil
}

// PixKeyIsExist checks if PIX key exists using external protocol
func (h *PixDictHandler) PixKeyIsExist(ctx context.Context, req *pixrequest.PixKeyIsExistRequest) (*pixresponse.PixKeyIsExistResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Get first key value for logging
	keyValue := ""
	if len(req.Keys) > 0 && req.Keys[0] != nil {
		keyValue = req.Keys[0].GetKeyValue()
	}

	logger.Info("Processing VerifyPixKeyExistence request",
		zap.String("pix_key", keyValue),
	)

	// Call service layer
	response, err := h.pixDictService.VerifyPixKeyExistence(ctx, req)
	if err != nil {
		logger.Error("Failed to check PIX key existence",
			zap.String("pix_key", keyValue),
			zap.Error(err),
		)
		// Create error response for existence check operation
		return &pixresponse.PixKeyIsExistResponse{
			Result: &pixresponse.PixKeyIsExistResponse_Error{
				Error: &pixresponse.Error{
					Error:        pixresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: err.Error(),
				},
			},
		}, nil
	}

	logger.Info("Successfully processed VerifyPixKeyExistence request",
		zap.String("pix_key", keyValue),
	)

	return response, nil
}

// PixKeyGet gets PIX key information using external protocol
func (h *PixDictHandler) PixKeyGet(ctx context.Context, req *pixrequest.PixKeyGetRequest) (*pixresponse.PixKeyGetResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Get key value for logging
	keyValue := ""
	if req.PixKey != nil {
		keyValue = req.PixKey.GetKeyValue()
	}

	logger.Info("Processing GetPixKey request",
		zap.String("pix_key", keyValue),
	)

	// Call service layer
	response, err := h.pixDictService.GetPixKey(ctx, req)
	if err != nil {
		logger.Error("Failed to get PIX key",
			zap.String("pix_key", keyValue),
			zap.Error(err),
		)
		// Create error response for get operation
		return &pixresponse.PixKeyGetResponse{
			Result: &pixresponse.PixKeyGetResponse_Error{
				Error: &pixresponse.Error{
					Error:        pixresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: err.Error(),
				},
			},
		}, nil
	}

	logger.Info("Successfully processed GetPixKey request",
		zap.String("pix_key", keyValue),
	)

	return response, nil
}

// NotifyAccountClosure notifies account closure using external protocol
func (h *PixDictHandler) NotifyAccountClosure(ctx context.Context, req *pixrequest.NotifyAccountClosureRequest) (*pixresponse.NotifyAccountClosureResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Get account number for logging
	accountNumber := ""
	if req.BankAccount != nil {
		accountNumber = req.BankAccount.GetAccountNumber()
	}

	logger.Info("Processing NotifyAccountClosure request",
		zap.String("account", accountNumber),
	)

	// Call service layer
	response, err := h.pixDictService.NotifyAccountClosure(ctx, req)
	if err != nil {
		logger.Error("Failed to notify account closure",
			zap.String("account", accountNumber),
			zap.Error(err),
		)
		// Create error response for notify operation
		return &pixresponse.NotifyAccountClosureResponse{
			Result: &pixresponse.NotifyAccountClosureResponse_Error{
				Error: &pixresponse.Error{
					Error:        pixresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: err.Error(),
				},
			},
		}, nil
	}

	logger.Info("Successfully processed NotifyAccountClosure request",
		zap.String("account", accountNumber),
	)

	return response, nil
}
