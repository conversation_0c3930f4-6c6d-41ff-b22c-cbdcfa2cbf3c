package handler

import (
	"context"

	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/service"
	"jdpi-gateway/internal/validator"

	"go.uber.org/zap"

	// External protocol imports
	pixservice "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix"
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
)

// PixQRCodeHandler PIX QRCode gRPC handler
type PixQRCodeHandler struct {
	pixservice.UnimplementedPixQRCodeServiceServer
	pixQRCodeService service.PixQRCodeService
	validator        *validator.PixQRCodeValidator
	logger           *zap.Logger
}

// NewPixQRCodeHandler creates a new PIX QRCode handler
func NewPixQRCodeHandler(pixQRCodeService service.PixQRCodeService, logger *zap.Logger) *PixQRCodeHandler {
	return &PixQRCodeHandler{
		pixQRCodeService: pixQRCodeService,
		validator:        validator.NewPixQRCodeValidator(),
		logger:           logger,
	}
}

// QRCodeStaticCreate creates a static QR code using external protocol
func (h *PixQRCodeHandler) QRCodeStaticCreate(ctx context.Context, req *pixrequest.QRCodeStaticCreateRequest) (*pixresponse.QRCodeStaticCreateResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	logger.Info("Processing QRCodeStaticCreate request",
		zap.String("tx_id", req.TxId),
		zap.String("pix_key", req.PixKey.GetKeyValue()),
		zap.String("pix_key_type", req.PixKey.GetKeyType().String()),
		zap.Float64("amount", req.Amount),
	)

	// Call service layer
	response, err := h.pixQRCodeService.CreateStaticQRCode(ctx, req)
	if err != nil {
		logger.Error("Failed to create static QR code", zap.Error(err))
		return nil, err
	}

	logger.Info("Successfully processed CreateStaticQRCode request", zap.String("tx_id", req.TxId), zap.String("pix_key", req.PixKey.GetKeyValue()))
	return response, nil
}

// QRCodeDynamicCreate creates a dynamic QR code using external protocol
func (h *PixQRCodeHandler) QRCodeDynamicCreate(ctx context.Context, req *pixrequest.QRCodeDynamicCreateRequest) (*pixresponse.QRCodeDynamicCreateResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	logger.Info("Processing QRCodeDynamicCreate request",
		zap.String("tx_id", req.TxId),
		zap.String("pix_key", req.PixKey.GetKeyValue()),
		zap.String("pix_key_type", req.PixKey.GetKeyType().String()),
		zap.Int32("expire_in", req.ExpireIn),
		zap.Bool("reusable", req.Reusable),
	)

	// Call service layer
	response, err := h.pixQRCodeService.CreateDynamicQRCode(ctx, req)
	if err != nil {
		logger.Error("Failed to create dynamic QR code", zap.Error(err))
		return nil, err
	}

	logger.Info("Successfully processed QRCodeDynamicCreate request", zap.String("tx_id", req.TxId), zap.String("pix_key", req.PixKey.GetKeyValue()))
	return response, nil
}

// QRCodeGetByPayload gets QR code information by payload using external protocol
func (h *PixQRCodeHandler) QRCodeGetByPayload(ctx context.Context, req *pixrequest.QRCodeGetByPayloadRequest) (*pixresponse.QRCodeGetByPayloadResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	logger.Info("Processing QRCodeGetByPayload request",
		zap.String("end_to_end_id", req.EndToEndId),
		zap.String("payer_document_id", req.PayerDocumentId),
		zap.String("qrcode_payload_length", func() string {
			if len(req.QrcodePayload) > 50 {
				return "50+"
			}
			return string(rune(len(req.QrcodePayload)))
		}()),
	)

	// Call service layer
	response, err := h.pixQRCodeService.DecodeQRCodeByPayload(ctx, req)
	if err != nil {
		logger.Error("Failed to get QR code by payload", zap.Error(err))
		return nil, err
	}

	logger.Info("Successfully processed QRCodeGetByPayload request", zap.String("end_to_end_id", req.EndToEndId))
	return response, nil
}

// QRCodeDynamicDecodeByUrl decodes dynamic QR code by URL using external protocol
func (h *PixQRCodeHandler) QRCodeDynamicDecodeByUrl(ctx context.Context, req *pixrequest.QRCodeDynamicDecodeByUrlRequest) (*pixresponse.QRCodeDynamicDecodeByUrlResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	logger.Info("Processing QRCodeDynamicDecodeByUrl request",
		zap.String("end_to_end_id", req.EndToEndId),
		zap.String("payer_document_id", req.PayerDocumentId),
		zap.String("qrcode_payload_url", req.QrcodePayloadUrl),
	)

	// Call service layer
	response, err := h.pixQRCodeService.DecodeDynamicQRCodeByUrl(ctx, req)
	if err != nil {
		logger.Error("Failed to decode dynamic QR code by URL", zap.Error(err))
		return nil, err
	}

	logger.Info("Successfully processed QRCodeDynamicDecodeByUrl request", zap.String("end_to_end_id", req.EndToEndId))
	return response, nil
}
