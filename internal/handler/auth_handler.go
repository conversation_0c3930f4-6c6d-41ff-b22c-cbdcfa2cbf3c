package handler

import (
	"context"
	"fmt"
	"time"

	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"
	"jdpi-gateway/internal/service"
	pb "jdpi-gateway/proto"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
)

// AuthHandler gRPC handler
type AuthHandler struct {
	pb.UnimplementedAuthServiceServer
	authService service.AuthService
	validator   *validator.Validate
	logger      *zap.Logger
}

// NewAuthHandler creates a new JDPI handler
func NewAuthHandler(authService service.AuthService, logger *zap.Logger) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		validator:   validator.New(),
		logger:      logger,
	}
}

// GetAccessToken retrieves the access token
func (h *AuthHandler) GetAccessToken(ctx context.Context, req *pb.GetAccessTokenRequest) (*pb.GetAccessTokenResponse, error) {
	// Get trace ID from context
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	// Parameter validation
	if err := h.validateGetAccessTokenRequest(req); err != nil {
		logger.Warn("Invalid request parameters",
			zap.Error(err),
			zap.Any("request", req),
		)
		return h.createErrorResponse(codes.InvalidArgument, "Invalid request parameters", err.Error()), nil
	}

	// Generate request ID (if not provided)
	requestID := req.GetRequestId()
	if requestID == "" {
		requestID = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}

	logger.Info("Processing GetAccessToken request",
		zap.String("request_id", requestID),
	)

	// Call service layer to get access token
	token, err := h.authService.GetAccessToken(ctx, requestID)
	if err != nil {
		logger.Error("Failed to get access token",
			zap.String("request_id", requestID),
			zap.Error(err),
		)
		return h.createErrorResponse(codes.Internal, "Failed to get access token", h.getErrorDetails(err)), nil
	}

	// Build success response
	response := &pb.GetAccessTokenResponse{
		Status: &pb.ResponseStatus{
			Code:    0,
			Message: "Success",
		},
		Data: &pb.AccessTokenData{
			AccessToken: token.AccessToken,
			TokenType:   token.TokenType,
			ExpiresIn:   token.ExpiresIn,
			Scope:       token.Scope,
			ExpiresAt:   token.ExpiresAt.Unix(),
		},
	}

	logger.Info("Successfully processed GetAccessToken request",
		zap.String("request_id", requestID),
		zap.String("token_type", token.TokenType),
		zap.Int64("expires_in", token.ExpiresIn),
		zap.Time("expires_at", token.ExpiresAt),
	)

	return response, nil
}

// validateGetAccessTokenRequest validates the GetAccessToken request
func (h *AuthHandler) validateGetAccessTokenRequest(req *pb.GetAccessTokenRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}

	// Additional validation logic can be added here
	// For example, validate request_id format, etc.

	return nil
}

// getErrorDetails extracts error details, prioritizing JD API raw response
func (h *AuthHandler) getErrorDetails(err error) string {
	if jdErr, ok := model.IsJDAPIError(err); ok {
		return jdErr.GetDetailsForResponse()
	}
	return err.Error()
}

// createErrorResponse creates an error response
func (h *AuthHandler) createErrorResponse(code codes.Code, message, details string) *pb.GetAccessTokenResponse {
	return &pb.GetAccessTokenResponse{
		Status: &pb.ResponseStatus{
			Code:    int32(code),
			Message: message,
			Details: details,
		},
		Data: nil,
	}
}

// Health health check (optional)
func (h *AuthHandler) Health(ctx context.Context) error {
	// Health check logic can be added here
	// For example, check if dependent services are available
	return nil
}

// GetTokenInfo retrieves token information (for debugging)
func (h *AuthHandler) GetTokenInfo() map[string]interface{} {
	if authService, ok := h.authService.(*service.JDAuthService); ok {
		return authService.GetTokenInfo()
	}
	return map[string]interface{}{
		"error": "unable to get token info",
	}
}
