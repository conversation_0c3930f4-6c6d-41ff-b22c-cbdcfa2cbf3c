package handler

import (
	"context"

	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/service"

	"go.uber.org/zap"

	// External protocol imports
	pixservice "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix"
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
)

// PixPaymentHandler PIX Payment gRPC handler
type PixPaymentHandler struct {
	pixservice.UnimplementedPixTransactionServiceServer
	pixPaymentService service.PixPaymentService
	logger            *zap.Logger
}

// NewPixPaymentHandler creates a new PIX Payment handler
func NewPixPaymentHandler(pixPaymentService service.PixPaymentService, logger *zap.Logger) *PixPaymentHandler {
	return &PixPaymentHandler{
		pixPaymentService: pixPaymentService,
		logger:            logger,
	}
}

// PixOutConfirm confirms a PIX payment using external protocol
func (h *PixPaymentHandler) PixOutConfirm(ctx context.Context, req *pixrequest.PixOutConfirmRequest) (*pixresponse.PixOutConfirmResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	logger.Info("Processing PixOutConfirm request",
		zap.String("request_id", req.RequestId),
		zap.String("end_to_end_id", req.EndToEndId),
		zap.Float64("amount", req.Amount),
		zap.String("transaction_type", req.TransactionType.String()),
		zap.String("purpose", req.Purpose.String()),
	)

	// Call service layer
	response, err := h.pixPaymentService.PixOutConfirm(ctx, req)
	if err != nil {
		logger.Error("Failed to process PixOutConfirm request", zap.Error(err))
		return nil, err
	}

	logger.Info("Successfully processed PixOutConfirm request")
	return response, nil
}

// PixTransactionGet gets PIX transaction status by EndToEndId using external protocol
func (h *PixPaymentHandler) PixTransactionGet(ctx context.Context, req *pixrequest.PixTransactionGetRequest) (*pixresponse.PixTransactionGetResponse, error) {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := h.logger.With(zap.String("trace_id", traceID))

	logger.Info("Processing PixTransactionGet request",
		zap.String("channel_request_id", req.ChannelRequestId),
	)

	// Call service layer
	response, err := h.pixPaymentService.GetPixPaymentOrder(ctx, req)
	if err != nil {
		logger.Error("Failed to process PixTransactionGet request", zap.Error(err))
		return nil, err
	}

	logger.Info("Successfully processed PixTransactionGet request")
	return response, nil
}
