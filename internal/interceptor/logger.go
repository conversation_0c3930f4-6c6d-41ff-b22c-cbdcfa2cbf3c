package interceptor

import (
	"context"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// LoggerInterceptor logging interceptor
func LoggerInterceptor(logger *zap.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		start := time.Now()

		// Get trace ID from context
		traceID := GetTraceIDFromContext(ctx)
		loggerWithTrace := logger.With(zap.String("trace_id", traceID))

		// Log request start
		loggerWithTrace.Info("gRPC request started",
			zap.String("method", info.FullMethod),
			zap.Any("request", req),
			zap.Time("start_time", start),
		)

		// Call the actual handler
		resp, err := handler(ctx, req)

		// Calculate processing time
		duration := time.Since(start)

		// Get status code
		code := codes.OK
		if err != nil {
			if st, ok := status.FromError(err); ok {
				code = st.Code()
			} else {
				code = codes.Internal
			}
		}

		// Log request completion
		if err != nil {
			loggerWithTrace.Error("gRPC request completed with error",
				zap.String("method", info.FullMethod),
				zap.Duration("duration", duration),
				zap.String("code", code.String()),
				zap.Error(err),
			)
		} else {
			loggerWithTrace.Info("gRPC request completed successfully",
				zap.String("method", info.FullMethod),
				zap.Duration("duration", duration),
				zap.String("code", code.String()),
				zap.Any("response", resp),
			)
		}

		return resp, err
	}
}

// StreamLoggerInterceptor streaming request logging interceptor
func StreamLoggerInterceptor(logger *zap.Logger) grpc.StreamServerInterceptor {
	return func(srv interface{}, stream grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		start := time.Now()

		// Get trace ID from context
		traceID := GetTraceIDFromContext(stream.Context())
		loggerWithTrace := logger.With(zap.String("trace_id", traceID))

		// Log stream request start
		loggerWithTrace.Info("gRPC stream started",
			zap.String("method", info.FullMethod),
			zap.Bool("client_stream", info.IsClientStream),
			zap.Bool("server_stream", info.IsServerStream),
			zap.Time("start_time", start),
		)

		// Call the actual handler
		err := handler(srv, stream)

		// Calculate processing time
		duration := time.Since(start)

		// Get status code
		code := codes.OK
		if err != nil {
			if st, ok := status.FromError(err); ok {
				code = st.Code()
			} else {
				code = codes.Internal
			}
		}

		// Log stream request completion
		if err != nil {
			loggerWithTrace.Error("gRPC stream completed with error",
				zap.String("method", info.FullMethod),
				zap.Duration("duration", duration),
				zap.String("code", code.String()),
				zap.Error(err),
			)
		} else {
			loggerWithTrace.Info("gRPC stream completed successfully",
				zap.String("method", info.FullMethod),
				zap.Duration("duration", duration),
				zap.String("code", code.String()),
			)
		}

		return err
	}
}
