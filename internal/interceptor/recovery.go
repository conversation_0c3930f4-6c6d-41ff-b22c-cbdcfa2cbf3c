package interceptor

import (
	"context"
	"runtime/debug"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// RecoveryInterceptor panic recovery interceptor
func RecoveryInterceptor(logger *zap.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		defer func() {
			if r := recover(); r != nil {
				// Log panic information
				logger.Error("gRPC handler panic recovered",
					zap.String("method", info.FullMethod),
					zap.Any("panic", r),
					zap.String("stack", string(debug.Stack())),
				)

				// Return internal server error
				err = status.Errorf(codes.Internal, "internal server error: %v", r)
			}
		}()

		return handler(ctx, req)
	}
}

// StreamRecoveryInterceptor streaming request panic recovery interceptor
func StreamRecoveryInterceptor(logger *zap.Logger) grpc.StreamServerInterceptor {
	return func(srv interface{}, stream grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) (err error) {
		defer func() {
			if r := recover(); r != nil {
				// Log panic information
				logger.Error("gRPC stream handler panic recovered",
					zap.String("method", info.FullMethod),
					zap.Any("panic", r),
					zap.String("stack", string(debug.Stack())),
				)

				// Return internal server error
				err = status.Errorf(codes.Internal, "internal server error: %v", r)
			}
		}()

		return handler(srv, stream)
	}
}

// ValidationInterceptor parameter validation interceptor
func ValidationInterceptor(logger *zap.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// General parameter validation logic can be added here
		// For example, check if the request is nil
		if req == nil {
			logger.Warn("Received nil request",
				zap.String("method", info.FullMethod),
			)
			return nil, status.Error(codes.InvalidArgument, "request cannot be nil")
		}

		return handler(ctx, req)
	}
}

// RequestIDInterceptor request ID interceptor
func RequestIDInterceptor(logger *zap.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Extract request ID from metadata or generate a new one
		// Simplified here; actual projects may require more complex logic

		// Add request ID to context
		// ctx = context.WithValue(ctx, "request_id", requestID)

		return handler(ctx, req)
	}
}

// RateLimitInterceptor rate limiting interceptor (example)
func RateLimitInterceptor(logger *zap.Logger, maxRequestsPerSecond int) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Implement rate limiting logic here
		// For example, use token bucket or sliding window algorithm

		// If limit exceeded, return error
		// return nil, status.Error(codes.ResourceExhausted, "rate limit exceeded")

		return handler(ctx, req)
	}
}
