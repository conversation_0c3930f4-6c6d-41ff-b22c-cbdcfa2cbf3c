package interceptor

import (
	"context"
	"encoding/hex"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	// TraceIDKey is the key used for trace ID in metadata
	TraceIDKey = "x-trace-id"
	// TraceIDContextKey is the key used for trace ID in context
	TraceIDContextKey = "trace_id"
)

// TraceIDInterceptor adds trace ID to gRPC requests
func TraceIDInterceptor(logger *zap.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Extract or generate trace ID
		traceID := extractOrGenerateTraceID(ctx)

		// Add trace ID to context
		ctx = context.WithValue(ctx, TraceIDContextKey, traceID)

		// Add trace ID to logger
		loggerWithTrace := logger.With(zap.String("trace_id", traceID))

		// Log request with trace ID
		loggerWithTrace.Info("gRPC request started",
			zap.String("method", info.FullMethod),
			zap.String("trace_id", traceID),
		)

		// Call the actual handler with enhanced context
		resp, err := handler(ctx, req)

		// Log response with trace ID
		if err != nil {
			loggerWithTrace.Error("gRPC request completed with error",
				zap.String("method", info.FullMethod),
				zap.String("trace_id", traceID),
				zap.Error(err),
			)
		} else {
			loggerWithTrace.Info("gRPC request completed successfully",
				zap.String("method", info.FullMethod),
				zap.String("trace_id", traceID),
			)
		}

		return resp, err
	}
}

// StreamTraceIDInterceptor adds trace ID to gRPC stream requests
func StreamTraceIDInterceptor(logger *zap.Logger) grpc.StreamServerInterceptor {
	return func(srv interface{}, stream grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		// Extract or generate trace ID
		traceID := extractOrGenerateTraceID(stream.Context())

		// Add trace ID to context
		ctx := context.WithValue(stream.Context(), TraceIDContextKey, traceID)

		// Create a new stream with enhanced context
		wrappedStream := &traceIDStream{
			ServerStream: stream,
			ctx:          ctx,
		}

		// Add trace ID to logger
		loggerWithTrace := logger.With(zap.String("trace_id", traceID))

		// Log stream start with trace ID
		loggerWithTrace.Info("gRPC stream started",
			zap.String("method", info.FullMethod),
			zap.String("trace_id", traceID),
		)

		// Call the actual handler
		err := handler(srv, wrappedStream)

		// Log stream completion with trace ID
		if err != nil {
			loggerWithTrace.Error("gRPC stream completed with error",
				zap.String("method", info.FullMethod),
				zap.String("trace_id", traceID),
				zap.Error(err),
			)
		} else {
			loggerWithTrace.Info("gRPC stream completed successfully",
				zap.String("method", info.FullMethod),
				zap.String("trace_id", traceID),
			)
		}

		return err
	}
}

// traceIDStream wraps grpc.ServerStream to add trace ID to context
type traceIDStream struct {
	grpc.ServerStream
	ctx context.Context
}

func (s *traceIDStream) Context() context.Context {
	return s.ctx
}

// extractOrGenerateTraceID extracts trace ID from metadata or generates a new one
func extractOrGenerateTraceID(ctx context.Context) string {
	// Try to extract from metadata first
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if traceIDs := md.Get(TraceIDKey); len(traceIDs) > 0 {
			return traceIDs[0]
		}
	}

	// Generate new trace ID if not found
	return generateTraceID()
}

// generateTraceID generates a unique trace ID with format T{UUID}
// TraceId format: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
func generateTraceID() string {
	u := uuid.New()
	return hex.EncodeToString(u[:])
}

// GenerateTraceID generates a unique trace ID with format T{UUID} (exported for testing)
func GenerateTraceID() string {
	return generateTraceID()
}

// GetTraceIDFromContext extracts trace ID from context
func GetTraceIDFromContext(ctx context.Context) string {
	if traceID, ok := ctx.Value(TraceIDContextKey).(string); ok {
		return traceID
	}
	return ""
}

// AddTraceIDToContext adds trace ID to context
func AddTraceIDToContext(ctx context.Context, traceID string) context.Context {
	return context.WithValue(ctx, TraceIDContextKey, traceID)
}

// AddTraceIDToMetadata adds trace ID to gRPC metadata
func AddTraceIDToMetadata(ctx context.Context, traceID string) context.Context {
	md := metadata.Pairs(TraceIDKey, traceID)
	return metadata.NewOutgoingContext(ctx, md)
}
