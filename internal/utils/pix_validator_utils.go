package utils

import (
	"fmt"
	"strconv"
	"strings"

	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

func ValidateDocumentID(documentID string) error {
	if documentID == "" {
		return fmt.Errorf("document ID is required")
	}

	// Remove any formatting characters (dots, hyphens, slashes)
	cleanDocumentID := strings.ReplaceAll(documentID, ".", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, "-", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, "/", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, " ", "")

	// Convert to int64
	_, err := strconv.ParseInt(cleanDocumentID, 10, 64)
	if err != nil {
		return fmt.Errorf("document ID should be a number")
	}

	return nil
}

// Validation helper functions

func ValidInitiationType(transactionType wallet.PixTransactionType) bool {
	switch transactionType {
	case wallet.PixTransactionType_MANUAL,
		wallet.PixTransactionType_PIX_KEY,
		wallet.PixTransactionType_STATIC_QRCODE,
		wallet.PixTransactionType_DYNAMIC_QRCODE,
		wallet.PixTransactionType_PAYMENT_INITIATION_SERVICE,
		wallet.PixTransactionType_PAYER_QRCODE,
		wallet.PixTransactionType_AUTOMATIC_PIX,
		wallet.PixTransactionType_PIX_APPROXIMATION:
		return true
	default:
		return false
	}
}

func ValidLiquidationPriority(priority wallet.PixTransactionPriority) bool {
	switch priority {
	case wallet.PixTransactionPriority_PRIORITY_LIQUIDATION,
		wallet.PixTransactionPriority_NON_PRIORITY_LIQUIDATION:
		return true
	default:
		return false
	}
}

func ValidPaymentPriority(priorityType wallet.PixTransactionPriorityType) bool {
	switch priorityType {
	case wallet.PixTransactionPriorityType_PRIORITY_PAYMENT,
		wallet.PixTransactionPriorityType_PAYMENT_UNDER_FRAUD_ANALYSIS,
		wallet.PixTransactionPriorityType_SCHEDULED_PAYMENT:
		return true
	default:
		return false
	}
}

func ValidPurpose(purpose wallet.PixTransactionPurpose) bool {
	switch purpose {
	case wallet.PixTransactionPurpose_PURCHASE_OR_TRANSFER,
		wallet.PixTransactionPurpose_PIX_CHANGE,
		wallet.PixTransactionPurpose_PIX_WITHDRAWAL:
		return true
	default:
		return false
	}
}

func ValidPersonType(personType wallet.AccountHolderType) bool {
	switch personType {
	case wallet.AccountHolderType_NATURAL,
		wallet.AccountHolderType_LEGAL:
		return true
	default:
		return false
	}
}

func ValidAccountType(accountType wallet.BankAccountType) bool {
	switch accountType {
	case wallet.BankAccountType_CHECKING_ACCOUNT,
		wallet.BankAccountType_SALARY_ACCOUNT,
		wallet.BankAccountType_SAVINGS_ACCOUNT,
		wallet.BankAccountType_PAYMENT_ACCOUNT:
		return true
	default:
		return false
	}
}
