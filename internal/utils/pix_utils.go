package utils

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// ConvertJDPixKeyTypeToEnum converts JD API PIX key type to protobuf PixKeyType enum
func ConvertJDPixKeyTypeToEnum(jdKeyType int) wallet.PixKeyType {
	if jdKeyType >= 0 && jdKeyType <= 4 {
		return wallet.PixKeyType(jdKeyType)
	}
	// Default to EVP for unknown types
	return wallet.PixKeyType_EVP
}

// ParseDocumentID converts document ID string to int64
// This is a package-level function that can be called directly without creating an instance
func ParseDocumentID(documentID string) int64 {
	if documentID == "" {
		return 0
	}

	// Remove any formatting characters (dots, hyphens, slashes)
	cleanDocumentID := strings.ReplaceAll(documentID, ".", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, "-", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, "/", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, " ", "")

	// Convert to int64
	result, err := strconv.ParseInt(cleanDocumentID, 10, 64)
	if err != nil {
		return 0
	}

	return result
}

// ParseTimestampSafe safely converts a string to *timestamppb.Timestamp, returning nil if conversion fails
// This is a package-level function that can be called directly without creating an instance
func ParseTimestampSafe(timeStr string) *timestamppb.Timestamp {
	if timeStr == "" {
		return nil
	}

	// Try to parse ISO 8601 format first
	if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
		return timestamppb.New(t)
	}

	// Try different timestamp formats
	formats := []string{
		"2006-01-02T15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02 15:04:05",
		"2006-01-02",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return timestamppb.New(t)
		}
	}

	return nil
}

// ParseInt safely converts a string to int, returning 0 if conversion fails
// This is a package-level function that can be called directly without creating an instance
func ParseInt(s string) int {
	if s == "" {
		return 0
	}
	val, err := strconv.Atoi(s)
	if err != nil {
		return 0
	}
	return val
}

// ParseInt64 safely converts a string to int64, returning 0 if conversion fails
// This is a package-level function that can be called directly without creating an instance
func ParseInt64(s string) int64 {
	if s == "" {
		return 0
	}
	val, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return 0
	}
	return val
}

// ConvertQRCodeResponseTypeToJD converts protobuf QRCodeResponseType to JD API format type
func ConvertQRCodeResponseTypeToJD(responseType wallet.QRCodeResponseType) int {
	switch responseType {
	case wallet.QRCodeResponseType_IMAGE_ONLY:
		return 0 // Image only
	case wallet.QRCodeResponseType_PAYLOAD_ONLY:
		return 1 // Payload only
	case wallet.QRCodeResponseType_BOTH:
		return 3 // Both image and payload
	default:
		return 3 // Default to both
	}
}

// ConvertJDToQRCodeResponseType converts JD API format type to protobuf QRCodeResponseType
func ConvertJDToQRCodeResponseType(jdFormat int) wallet.QRCodeResponseType {
	if jdFormat == 0 {
		return wallet.QRCodeResponseType_IMAGE_ONLY
	}
	if jdFormat == 1 {
		return wallet.QRCodeResponseType_PAYLOAD_ONLY
	}
	if jdFormat == 3 {
		return wallet.QRCodeResponseType_BOTH
	}

	return wallet.QRCodeResponseType_BOTH
}

// ConvertPixKeyTypeToJD converts protobuf PixKeyType to JD API PIX key type
func ConvertPixKeyTypeToJD(keyType wallet.PixKeyType) int {
	return int(keyType)
}

// GetCategoryCodeForQRCode returns the appropriate category code for QR code
func GetCategoryCodeForQRCode() string {
	return "0014" // Standard category code for QR codes
}

// ValidateQRCodeAmount validates QR code amount
func ValidateQRCodeAmount(amount float64) error {
	if amount < 0 {
		return fmt.Errorf("amount cannot be negative")
	}
	if amount > 999999999.99 {
		return fmt.Errorf("amount exceeds maximum allowed value")
	}
	return nil
}

// ConvertJDToPixTransactionType converts JD API format to proto PixTransactionType
func ConvertJDToPixTransactionType(jdType int) wallet.PixTransactionType {
	if jdType >= 0 && jdType <= 9 {
		return wallet.PixTransactionType(jdType)
	}
	// Default
	return wallet.PixTransactionType_MANUAL
}

// ConvertPixTransactionPriorityToJD converts proto PixLiquidationPriority to JD API format
func ConvertPixTransactionPriorityToJD(priority wallet.PixTransactionPriority) int {
	return int(priority)
}

// ConvertPixTransactionPriorityTypeToJD converts proto PixPaymentPriority to JD API format
func ConvertPixTransactionPriorityTypeToJD(priority wallet.PixTransactionPriorityType) int {
	return int(priority)
}

// ConvertPixTransactionPurposeToJD converts proto PixPurpose to JD API format
func ConvertPixTransactionPurposeToJD(purpose wallet.PixTransactionPurpose) int {
	return int(purpose)
}

// ConvertPixPersonTypeToJD converts proto PixPersonType to JD API format
func ConvertPixPersonTypeToJD(personType wallet.AccountHolderType) int {
	return int(personType)
}

// ConvertJDToPixPersonType converts JD API format to proto AccountHolderType
func ConvertJDToPixPersonType(jdType int) wallet.AccountHolderType {
	if jdType >= 0 && jdType <= 1 {
		return wallet.AccountHolderType(jdType)
	}
	return wallet.AccountHolderType_NATURAL
}

// ConvertJDToPixAccountType converts JD API format to proto BankAccountType
func ConvertJDToPixAccountType(jdType int) wallet.BankAccountType {
	if jdType >= 0 && jdType <= 3 {
		return wallet.BankAccountType(jdType)
	}
	return wallet.BankAccountType_CHECKING_ACCOUNT
}

// ConvertJDToPixAmountType converts JD API format to proto PixAmountType
func ConvertJDToPixAmountType(jdType int) wallet.PixAmountType {
	if jdType >= 0 && jdType <= 1 {
		return wallet.PixAmountType(jdType)
	}
	return wallet.PixAmountType_CASH_AVAILABLE
}

// ConvertJDToPixAgentModality converts JD API format to proto PixWithdrawalAgentType
func ConvertJDToPixAgentModality(jdModality *int) wallet.PixWithdrawalAgentType {
	if jdModality == nil {
		return wallet.PixWithdrawalAgentType_WITHDRAWAL_FACILITATOR // Default
	}

	if *jdModality >= 0 && *jdModality <= 2 {
		return wallet.PixWithdrawalAgentType(*jdModality)
	}
	return wallet.PixWithdrawalAgentType_WITHDRAWAL_FACILITATOR
}

// ConvertPixAccountTypeToJD converts proto PixAccountType to JD API format

// ConvertPixWithdrawAgentTypeToJD converts proto PixAgentModality to JD API format (returns pointer for optional field)

func ConvertJDToQRCodeType(codeType int) wallet.QRCodeType {
	if codeType >= 11 && codeType <= 14 {
		return wallet.QRCodeType(codeType)
	}
	return wallet.QRCodeType_NOT_SET
}

func ConvertJDToPixTransactionStatus(status int) wallet.PixTransactionStatus {
	if status == 9 {
		return wallet.PixTransactionStatus_TRANSACTION_SUCCESS
	}
	if status == 10 {
		return wallet.PixTransactionStatus_TRANSACTION_ERROR
	}
	if status == 0 {
		return wallet.PixTransactionStatus_TRANSACTION_WAITING
	}
	return wallet.PixTransactionStatus_TRANSACTION_WAITING
}

func ConvertJDToPixIntermediateStatus(status int) wallet.PixTransactionIntermediateStatus {
	if status >= 0 && status <= 9 {
		return wallet.PixTransactionIntermediateStatus(status)
	}
	return wallet.PixTransactionIntermediateStatus_SUCCESS
}

func ConvertJDToPixLiquidationPriority(priority int) wallet.PixTransactionPriority {
	if priority >= 0 && priority <= 1 {
		return wallet.PixTransactionPriority(priority)
	}
	return wallet.PixTransactionPriority_PRIORITY_LIQUIDATION
}

func ConvertJDToPixPaymentPriority(transactionPriorityType int) wallet.PixTransactionPriorityType {
	if transactionPriorityType >= 0 && transactionPriorityType <= 2 {
		return wallet.PixTransactionPriorityType(transactionPriorityType)
	}
	return wallet.PixTransactionPriorityType_PRIORITY_PAYMENT
}

func ConvertJDToPixPurpose(purpose int) wallet.PixTransactionPurpose {
	if purpose >= 0 && purpose <= 2 {
		return wallet.PixTransactionPurpose(purpose)
	}
	return wallet.PixTransactionPurpose_PURCHASE_OR_TRANSFER
}
