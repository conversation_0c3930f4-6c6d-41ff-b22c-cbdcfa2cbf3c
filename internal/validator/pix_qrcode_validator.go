package validator

import (
	"fmt"

	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	"jdpi-gateway/internal/utils"
)

// PixQRCodeValidator validates PIX QRCode related requests
type PixQRCodeValidator struct{}

// NewPixQRCodeValidator creates a new PIX QRCode validator
func NewPixQRCodeValidator() *PixQRCodeValidator {
	return &PixQRCodeValidator{}
}

// ValidateQRCodeStaticCreateRequest validates QRCodeStaticCreateRequest request
func (v *PixQRCodeValidator) ValidateQRCodeStaticCreateRequest(req *pixrequest.QRCodeStaticCreateRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	if req.Header == nil {
		validationErrors.AddError("header", "is required")
	} else if req.Header.IdempotenceId == "" {
		validationErrors.AddError("header.idempotence_id", "is required")
	}

	if req.TxId == "" {
		validationErrors.AddError("tx_id", "is required")
	}

	if req.PixKey == nil {
		validationErrors.AddError("pix_key", "is required")
	} else {
		if req.PixKey.KeyValue == "" && req.PixKey.KeyType != wallet.PixKeyType_EVP {
			validationErrors.AddError("pix_key.key_value", "is required when key_type is not EVP")
		}
	}

	if req.PixKeyOwnerName == "" {
		validationErrors.AddError("pix_key_owner_name", "is required")
	}

	if req.PixKeyCity == "" {
		validationErrors.AddError("pix_key_city", "is required")
	}

	// Amount is optional for static QR codes, but if provided should be positive
	if req.Amount <= 0 {
		validationErrors.AddError("amount", "must be greater than 0")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// ValidateQRCodeDynamicCreateRequest validates QRCodeDynamicCreateRequest request
func (v *PixQRCodeValidator) ValidateQRCodeDynamicCreateRequest(req *pixrequest.QRCodeDynamicCreateRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	if req.Header == nil {
		validationErrors.AddError("header", "is required")
	} else if req.Header.IdempotenceId == "" {
		validationErrors.AddError("header.idempotence_id", "is required")
	}

	if req.TxId == "" {
		validationErrors.AddError("tx_id", "is required")
	}

	if req.PixKey == nil {
		validationErrors.AddError("pix_key", "is required")
	} else {
		if req.PixKey.KeyValue == "" && req.PixKey.KeyType != wallet.PixKeyType_EVP {
			validationErrors.AddError("pix_key.key_value", "is required when key_type is not EVP")
		}
	}

	if req.PixKeyOwnerName == "" {
		validationErrors.AddError("pix_key_owner_name", "is required")
	}

	if req.PixKeyCity == "" {
		validationErrors.AddError("pix_key_city", "is required")
	}

	// Validate payer information if provided
	if req.Payer != nil {
		if err := utils.ValidateDocumentID(req.Payer.DocumentId); err != nil {
			validationErrors.AddError("payer.document_id", fmt.Sprintf("%v", err))
		}
	}

	// Validate expiration time (should be positive if provided)
	if req.ExpireIn < 0 {
		validationErrors.AddError("expire_in", "must be greater than or equal to 0")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// ValidateQRCodeGetByPayloadRequest validates QRCodeGetByPayloadRequest request
func (v *PixQRCodeValidator) ValidateQRCodeGetByPayloadRequest(req *pixrequest.QRCodeGetByPayloadRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	if req.Header == nil {
		validationErrors.AddError("header", "is required")
	} else if req.Header.IdempotenceId == "" {
		validationErrors.AddError("header.idempotence_id", "is required")
	}

	if req.EndToEndId == "" {
		validationErrors.AddError("end_to_end_id", "is required")
	}

	if err := utils.ValidateDocumentID(req.PayerDocumentId); err != nil {
		validationErrors.AddError("payer_document_id", fmt.Sprintf("%v", err))
	}

	if req.QrcodePayload == "" {
		validationErrors.AddError("qrcode_payload", "is required")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// ValidateQRCodeDynamicDecodeByUrlRequest validates QRCodeDynamicDecodeByUrlRequest request
func (v *PixQRCodeValidator) ValidateQRCodeDynamicDecodeByUrlRequest(req *pixrequest.QRCodeDynamicDecodeByUrlRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	if req.Header == nil {
		validationErrors.AddError("header", "is required")
	} else if req.Header.IdempotenceId == "" {
		validationErrors.AddError("header.idempotence_id", "is required")
	}

	if req.EndToEndId == "" {
		validationErrors.AddError("end_to_end_id", "is required")
	}

	if err := utils.ValidateDocumentID(req.PayerDocumentId); err != nil {
		validationErrors.AddError("payer_document_id", fmt.Sprintf("%v", err))
	}

	if req.QrcodePayloadUrl == "" {
		validationErrors.AddError("qrcode_payload_url", "is required")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}
