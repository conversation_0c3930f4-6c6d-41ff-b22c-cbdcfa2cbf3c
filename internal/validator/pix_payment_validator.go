package validator

import (
	"fmt"
	"strings"

	"github.com/go-playground/validator/v10"
	pixservice "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
	"jdpi-gateway/internal/utils"
)

// PixPaymentValidator handles validation for PIX Payment operations
type PixPaymentValidator struct {
	validator *validator.Validate
}

// NewPixPaymentValidator creates a new PIX Payment validator
func NewPixPaymentValidator() *PixPaymentValidator {
	v := validator.New()

	return &PixPaymentValidator{
		validator: v,
	}
}

// ValidatePixOutConfirmRequest validates PixOutConfirm request
func (v *PixPaymentValidator) ValidatePixOutConfirmRequest(req *pixservice.PixOutConfirmRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	// Validate request ID
	if req.RequestId == "" {
		validationErrors.AddError("request_id", "is required")
	}

	// Validate end-to-end ID
	if req.EndToEndId == "" {
		validationErrors.AddError("end_to_end_id", "is required")
	}

	// Validate amount
	if req.Amount <= 0 {
		validationErrors.AddError("amount", "must be positive")
	}

	// Validate payer
	if req.Payer == nil {
		validationErrors.AddError("payer", "is required")
	} else {
		v.validatePixParticipant(req.Payer, "payer", validationErrors)
	}

	if req.PayerHolder == nil {
		validationErrors.AddError("payer_holder", "is required")
	}

	// Validate payee
	if req.Payee == nil {
		validationErrors.AddError("payee", "is required")
	} else {
		v.validatePixParticipant(req.Payee, "payee", validationErrors)
	}

	if req.PayeeHolder == nil {
		validationErrors.AddError("payee_holder", "is required")
	}

	// Validate initiation type
	if !utils.ValidInitiationType(req.TransactionType) {
		validationErrors.AddError("transaction_type", "invalid initiation_type")
	}

	// Validate liquidation priority
	if !utils.ValidLiquidationPriority(req.LiquidationPriority) {
		validationErrors.AddError("liquidation_priority", "invalid liquidation_priority")
	}

	// Validate payment priority
	if !utils.ValidPaymentPriority(req.PaymentPriority) {
		validationErrors.AddError("payment_priority", "invalid payment_priority")
	}

	// Validate purpose
	if !utils.ValidPurpose(req.Purpose) {
		validationErrors.AddError("purpose", "invalid purpose")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// ValidatePixTransactionGetRequest validates PixTransactionGet request
func (v *PixPaymentValidator) ValidatePixTransactionGetRequest(req *pixservice.PixTransactionGetRequest) error {
	validationErrors := &ValidationErrors{}

	if req.ChannelRequestId == "" {
		validationErrors.AddError("channel_request_id", "is required")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// validatePixParticipant validates PIX participant (payer or payee)
func (v *PixPaymentValidator) validatePixParticipant(participant *wallet.BankAccount, fieldPrefix string, validationErrors *ValidationErrors) {
	// Validate ISPB
	if participant.Ispb <= 0 {
		validationErrors.AddError(fmt.Sprintf("%s.ispb", fieldPrefix), "ispb must be greater than 0")
	}

	// Validate branch code
	if strings.TrimSpace(participant.BranchCode) == "" {
		validationErrors.AddError(fmt.Sprintf("%s.branch_code", fieldPrefix), "agency_number is required")
	}

	// Validate account number
	if strings.TrimSpace(participant.AccountNumber) == "" {
		validationErrors.AddError(fmt.Sprintf("%s.account_number", fieldPrefix), "account_number is required")
	}

	// Validate account type
	if !utils.ValidAccountType(participant.AccountType) {
		validationErrors.AddError(fmt.Sprintf("%s.account_type", fieldPrefix), "invalid account_type")
	}
}
