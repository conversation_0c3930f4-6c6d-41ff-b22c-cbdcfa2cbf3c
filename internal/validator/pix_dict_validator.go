package validator

import (
	"fmt"
	"strconv"
	"strings"

	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// PixDictValidator validates PIX Dict related requests
type PixDictValidator struct{}

// NewPixDictValidator creates a new PIX Dict validator
func NewPixDictValidator() *PixDictValidator {
	return &PixDictValidator{}
}

// ValidationError represents a validation error with field name and message
type ValidationError struct {
	Field   string
	Message string
}

// ValidationErrors represents multiple validation errors
type ValidationErrors struct {
	Errors []ValidationError
}

// Error implements the error interface
func (v *ValidationErrors) Error() string {
	if len(v.Errors) == 0 {
		return "no validation errors"
	}

	var messages []string
	for _, err := range v.Errors {
		messages = append(messages, fmt.Sprintf("%s: %s", err.Field, err.Message))
	}
	return strings.Join(messages, "; ")
}

// AddError adds a validation error
func (v *ValidationErrors) AddError(field, message string) {
	v.Errors = append(v.Errors, ValidationError{Field: field, Message: message})
}

// HasErrors checks if there are any validation errors
func (v *ValidationErrors) HasErrors() bool {
	return len(v.Errors) > 0
}

// ValidatePixKeyCreateRequest validates CreatePixKey request
func (v *PixDictValidator) ValidatePixKeyCreateRequest(req *pixrequest.PixKeyCreateRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	// if req.Header == nil {
	//	validationErrors.AddError("header", "is required")
	// }

	// if req.Header.IdempotenceId == "" {
	//	validationErrors.AddError("idempotence_id", "is required")
	// }

	if req.PixKey == nil {
		validationErrors.AddError("pix_key", "is required")
	} else if req.PixKey.KeyValue == "" && req.PixKey.KeyType != wallet.PixKeyType_EVP {
		validationErrors.AddError("pix_key.value", "is required when key_type is not EVP")
	}

	if req.Ispb == 0 {
		validationErrors.AddError("ispb", "is required")
	}

	if req.BankAccount == nil {
		validationErrors.AddError("bank_account", "is required")
	} else {
		if req.BankAccount.BranchCode == "" {
			validationErrors.AddError("bank_account.branch_code", "is required")
		}
		if req.BankAccount.AccountNumber == "" {
			validationErrors.AddError("bank_account.account_number", "is required")
		}
		if req.BankAccount.AccountOpeningDatetime == nil {
			validationErrors.AddError("bank_account.account_opening_datetime", "is required")
		} else if !req.BankAccount.AccountOpeningDatetime.IsValid() {
			validationErrors.AddError("bank_account.account_opening_datetime", "is not valid")
		}
	}

	if req.BankAccountHolder == nil {
		validationErrors.AddError("bank_account_holder", "is required")
	} else {
		if req.BankAccountHolder.HolderName == "" {
			validationErrors.AddError("bank_account_holder.holder_name", "is required")
		}
		if err := v.ValidateDocumentID(req.BankAccountHolder.DocumentId); err != nil {
			validationErrors.AddError("bank_account_holder.document_id", fmt.Sprintf("%v", err))
		}
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// ValidatePixKeyDeleteRequest validates DeletePixKey request
func (v *PixDictValidator) ValidatePixKeyDeleteRequest(req *pixrequest.PixKeyDeleteRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}
	// if req.Header == nil {
	//	validationErrors.AddError("header", "is required")
	// } else if req.Header.IdempotenceId == "" {
	//	validationErrors.AddError("header.idempotence_id", "is required")
	// }
	if req.PixKey == nil {
		validationErrors.AddError("pix_key", "is required")
	} else if req.PixKey.KeyValue == "" {
		validationErrors.AddError("pix_key.value", "is required when key_type is not EVP")
	}
	if req.Ispb == 0 {
		validationErrors.AddError("ispb", "is required")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}
	return nil
}

// ValidatePixKeyUpdateRequest validates UpdatePixKey request
func (v *PixDictValidator) ValidatePixKeyUpdateRequest(req *pixrequest.PixKeyUpdateRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	if req.PixKey == nil {
		validationErrors.AddError("pix_key", "is required")
	} else if req.PixKey.KeyValue == "" {
		validationErrors.AddError("pix_key.value", "is required")
	}

	if req.BankAccount == nil {
		validationErrors.AddError("bank_account", "is required")
	} else {
		if req.BankAccount.BranchCode == "" {
			validationErrors.AddError("bank_account.branch_code", "is required")
		}
		if req.BankAccount.AccountNumber == "" {
			validationErrors.AddError("bank_account.account_number", "is required")
		}
	}

	if req.BankAccountHolder == nil {
		validationErrors.AddError("bank_account_holder", "is required")
	} else {
		if req.BankAccountHolder.HolderName == "" {
			validationErrors.AddError("bank_account_holder.holder_name", "is required")
		}
	}

	if req.Ispb <= 0 {
		validationErrors.AddError("ispb", "must be greater than 0")
	}

	if req.Reason <= 0 {
		validationErrors.AddError("reason", "must be greater than 0")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// ValidatePixKeyListByAccountRequest validates ListPixKeys request
func (v *PixDictValidator) ValidatePixKeyListByAccountRequest(req *pixrequest.PixKeyListByAccountRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	if req.BankAccount == nil {
		validationErrors.AddError("bank_account", "is required")
	} else {
		if req.BankAccount.BranchCode == "" {
			validationErrors.AddError("bank_account.branch_code", "is required")
		}
		if req.BankAccount.AccountNumber == "" {
			validationErrors.AddError("bank_account.account_number", "is required")
		}
	}

	if req.BankAccountHolder == nil {
		validationErrors.AddError("bank_account_holder", "is required")
	} else {
		// Validate document ID format
		if err := v.ValidateDocumentID(req.BankAccountHolder.DocumentId); err != nil {
			validationErrors.AddError("bank_account_holder.document_id", fmt.Sprintf("%v", err))
		}
	}

	if req.Ispb <= 0 {
		validationErrors.AddError("ispb", "must be greater than 0")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// ValidatePixKeyIsExistRequest validates VerifyPixKeyExistence request
func (v *PixDictValidator) ValidatePixKeyIsExistRequest(req *pixrequest.PixKeyIsExistRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	if len(req.Keys) == 0 {
		validationErrors.AddError("keys", "cannot be empty")
	} else {
		for i, key := range req.Keys {
			if key == nil {
				validationErrors.AddError(fmt.Sprintf("keys[%d]", i), "cannot be nil")
			} else if key.KeyValue == "" {
				validationErrors.AddError(fmt.Sprintf("keys[%d].key_value", i), "is required")
			}
		}
	}

	if req.Ispb <= 0 {
		validationErrors.AddError("ispb", "must be greater than 0")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// ValidatePixKeyGetRequest validates GetPixKey request
func (v *PixDictValidator) ValidatePixKeyGetRequest(req *pixrequest.PixKeyGetRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	if req.PixKey == nil {
		validationErrors.AddError("pix_key", "is required")
	} else if req.PixKey.KeyValue == "" {
		validationErrors.AddError("pix_key.value", "is required")
	}

	// if req.E2EId == "" {
	//	validationErrors.AddError("e2e_id", "is required")
	// }

	// Validate document ID format
	if err := v.ValidateDocumentID(req.RequesterDocumentId); err != nil {
		validationErrors.AddError("bank_account_holder.document_id", fmt.Sprintf("%v", err))
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

// ValidateNotifyAccountClosureRequest validates NotifyAccountClosure request
func (v *PixDictValidator) ValidateNotifyAccountClosureRequest(req *pixrequest.NotifyAccountClosureRequest) error {
	validationErrors := &ValidationErrors{}

	if req == nil {
		validationErrors.AddError("request", "cannot be nil")
		return validationErrors
	}

	if req.BankAccount == nil {
		validationErrors.AddError("bank_account", "is required")
	} else {
		if req.BankAccount.BranchCode == "" {
			validationErrors.AddError("bank_account.branch_code", "is required")
		}
		if req.BankAccount.AccountNumber == "" {
			validationErrors.AddError("bank_account.account_number", "is required")
		}
	}

	if req.Ispb <= 0 {
		validationErrors.AddError("ispb", "must be greater than 0")
	}

	if validationErrors.HasErrors() {
		return validationErrors
	}

	return nil
}

func (v *PixDictValidator) ValidateDocumentID(documentID string) error {
	if documentID == "" {
		return fmt.Errorf("document ID is required")
	}

	// Remove any formatting characters (dots, hyphens, slashes)
	cleanDocumentID := strings.ReplaceAll(documentID, ".", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, "-", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, "/", "")
	cleanDocumentID = strings.ReplaceAll(cleanDocumentID, " ", "")

	// Convert to int64
	_, err := strconv.ParseInt(cleanDocumentID, 10, 64)
	if err != nil {
		return fmt.Errorf("document ID should be a number")
	}

	return nil
}
