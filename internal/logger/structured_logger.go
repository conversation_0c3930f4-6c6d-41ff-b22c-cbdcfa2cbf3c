package logger

import (
	"strings"
)

// 敏感信息脱敏工具函数

// MaskSensitiveData 通用敏感数据脱敏
func MaskSensitiveData(data string) string {
	if len(data) <= 8 {
		return "***"
	}
	return data[:4] + "***" + data[len(data)-4:]
}

// MaskAccount 脱敏账户信息
func MaskAccount(account string) string {
	return MaskSensitiveData(account)
}

// MaskPixKey 脱敏PIX Key（根据类型决定脱敏程度）
func MaskPixKey(pixKey, keyType string) string {
	switch keyType {
	case "CPF", "CNPJ":
		return MaskSensitiveData(pixKey)
	case "EMAIL":
		// 邮箱特殊处理
		if len(pixKey) > 0 {
			parts := []rune(pixKey)
			for i := 1; i < len(parts)-1; i++ {
				if parts[i] != '@' && parts[i] != '.' {
					parts[i] = '*'
				}
			}
			return string(parts)
		}
		return "***"
	case "PHONE":
		return MaskSensitiveData(pixKey)
	default:
		// 随机key等其他类型
		return MaskSensitiveData(pixKey)
	}
}

// 业务上下文格式化工具函数

// FormatBusinessContext 格式化业务上下文信息为字符串
func FormatBusinessContext(params map[string]interface{}) string {
	if len(params) == 0 {
		return ""
	}

	var parts []string
	for key, value := range params {
		parts = append(parts, key+"="+toString(value))
	}
	return strings.Join(parts, ", ")
}

// toString 将interface{}转换为字符串
func toString(value interface{}) string {
	switch v := value.(type) {
	case string:
		return v
	case int:
		return string(rune(v))
	case float64:
		return string(rune(int(v)))
	default:
		return "unknown"
	}
}

// 常用的业务上下文格式化函数
func FormatPixKeyContext(account, pixKey, keyType string) string {
	return "account=" + MaskAccount(account) + ", pix_key=" + MaskPixKey(pixKey, keyType) + ", key_type=" + keyType
}

func FormatQRCodeContext(txID, pixKey, keyType string, amount float64) string {
	return "tx_id=" + txID + ", pix_key=" + MaskPixKey(pixKey, keyType) + ", key_type=" + keyType + ", amount=" + toString(amount)
}

func FormatPaymentContext(txID, pixKey string, amount float64) string {
	return "tx_id=" + txID + ", pix_key=" + MaskPixKey(pixKey, "") + ", amount=" + toString(amount)
}
