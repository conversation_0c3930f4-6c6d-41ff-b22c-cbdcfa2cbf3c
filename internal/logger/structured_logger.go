package logger

import (
	"context"
	"fmt"
	"runtime/debug"
	"time"

	"jdpi-gateway/internal/interceptor"

	"go.uber.org/zap"
)

// StructuredLogger 结构化日志记录器，遵循JDPI Gateway日志规范
type StructuredLogger struct {
	logger    *zap.Logger
	traceID   string
	operation string
	component string
}

// NewStructuredLogger 创建新的结构化日志记录器
func NewStructuredLogger(logger *zap.Logger, component string) *StructuredLogger {
	return &StructuredLogger{
		logger:    logger,
		component: component,
	}
}

// WithContext 从上下文中提取trace_id并创建新的日志记录器
func (sl *StructuredLogger) WithContext(ctx context.Context) *StructuredLogger {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	return &StructuredLogger{
		logger:    sl.logger,
		traceID:   traceID,
		operation: sl.operation,
		component: sl.component,
	}
}

// WithOperation 设置操作名称
func (sl *StructuredLogger) WithOperation(operation string) *StructuredLogger {
	return &StructuredLogger{
		logger:    sl.logger,
		traceID:   sl.traceID,
		operation: operation,
		component: sl.component,
	}
}

// WithComponent 设置组件名称
func (sl *StructuredLogger) WithComponent(component string) *StructuredLogger {
	return &StructuredLogger{
		logger:    sl.logger,
		traceID:   sl.traceID,
		operation: sl.operation,
		component: component,
	}
}

// getBaseFields 获取基础字段
func (sl *StructuredLogger) getBaseFields() []zap.Field {
	fields := make([]zap.Field, 0, 3)
	
	if sl.traceID != "" {
		fields = append(fields, zap.String("trace_id", sl.traceID))
	}
	if sl.operation != "" {
		fields = append(fields, zap.String("operation", sl.operation))
	}
	if sl.component != "" {
		fields = append(fields, zap.String("component", sl.component))
	}
	
	return fields
}

// Info 记录INFO级别日志
func (sl *StructuredLogger) Info(message string, additionalFields ...zap.Field) {
	fields := sl.getBaseFields()
	fields = append(fields, additionalFields...)
	sl.logger.Info(message, fields...)
}

// Error 记录ERROR级别日志
func (sl *StructuredLogger) Error(message string, err error, additionalFields ...zap.Field) {
	fields := sl.getBaseFields()
	if err != nil {
		fields = append(fields, zap.Error(err))
	}
	fields = append(fields, additionalFields...)
	sl.logger.Error(message, fields...)
}

// ErrorWithStack 记录ERROR级别日志并包含堆栈跟踪
func (sl *StructuredLogger) ErrorWithStack(message string, err error, additionalFields ...zap.Field) {
	fields := sl.getBaseFields()
	if err != nil {
		fields = append(fields, zap.Error(err))
	}
	fields = append(fields, zap.String("stack_trace", string(debug.Stack())))
	fields = append(fields, additionalFields...)
	sl.logger.Error(message, fields...)
}

// Warn 记录WARN级别日志
func (sl *StructuredLogger) Warn(message string, additionalFields ...zap.Field) {
	fields := sl.getBaseFields()
	fields = append(fields, additionalFields...)
	sl.logger.Warn(message, fields...)
}

// Debug 记录DEBUG级别日志
func (sl *StructuredLogger) Debug(message string, additionalFields ...zap.Field) {
	fields := sl.getBaseFields()
	fields = append(fields, additionalFields...)
	sl.logger.Debug(message, fields...)
}

// BusinessOperationLogger 业务操作日志记录器
type BusinessOperationLogger struct {
	*StructuredLogger
	startTime time.Time
}

// StartOperation 开始业务操作记录
func (sl *StructuredLogger) StartOperation(businessContext string, additionalFields ...zap.Field) *BusinessOperationLogger {
	startTime := time.Now()
	
	message := fmt.Sprintf("Starting %s operation: %s", sl.operation, businessContext)
	fields := []zap.Field{zap.Time("start_time", startTime)}
	fields = append(fields, additionalFields...)
	
	sl.Info(message, fields...)
	
	return &BusinessOperationLogger{
		StructuredLogger: sl,
		startTime:        startTime,
	}
}

// CompleteOperation 完成业务操作记录
func (bol *BusinessOperationLogger) CompleteOperation(businessContext string, additionalFields ...zap.Field) {
	duration := time.Since(bol.startTime)
	
	message := fmt.Sprintf("Successfully completed %s operation: %s, duration=%v", 
		bol.operation, businessContext, duration)
	fields := []zap.Field{zap.Duration("duration", duration)}
	fields = append(fields, additionalFields...)
	
	bol.Info(message, fields...)
}

// FailOperation 失败业务操作记录
func (bol *BusinessOperationLogger) FailOperation(businessContext string, err error, additionalFields ...zap.Field) {
	duration := time.Since(bol.startTime)
	
	message := fmt.Sprintf("Failed to complete %s operation: %s, duration=%v, error=%s", 
		bol.operation, businessContext, duration, err.Error())
	fields := []zap.Field{zap.Duration("duration", duration)}
	fields = append(fields, additionalFields...)
	
	bol.Error(message, err, fields...)
}

// APICallLogger API调用日志记录器
type APICallLogger struct {
	*StructuredLogger
	startTime time.Time
	apiName   string
}

// StartAPICall 开始API调用记录
func (sl *StructuredLogger) StartAPICall(apiName, businessContext string, additionalFields ...zap.Field) *APICallLogger {
	startTime := time.Now()
	
	message := fmt.Sprintf("Calling %s API: %s", apiName, businessContext)
	fields := []zap.Field{zap.Time("start_time", startTime)}
	fields = append(fields, additionalFields...)
	
	sl.Info(message, fields...)
	
	return &APICallLogger{
		StructuredLogger: sl,
		startTime:        startTime,
		apiName:          apiName,
	}
}

// CompleteAPICall 完成API调用记录
func (acl *APICallLogger) CompleteAPICall(businessContext string, additionalFields ...zap.Field) {
	duration := time.Since(acl.startTime)
	
	message := fmt.Sprintf("Successfully called %s API: %s, duration=%v", 
		acl.apiName, businessContext, duration)
	fields := []zap.Field{zap.Duration("duration", duration)}
	fields = append(fields, additionalFields...)
	
	acl.Info(message, fields...)
}

// FailAPICall 失败API调用记录
func (acl *APICallLogger) FailAPICall(businessContext string, err error, statusCode int, additionalFields ...zap.Field) {
	duration := time.Since(acl.startTime)
	
	message := fmt.Sprintf("Failed to call %s API: %s, duration=%v, status=%d, error=%s", 
		acl.apiName, businessContext, duration, statusCode, err.Error())
	fields := []zap.Field{
		zap.Duration("duration", duration),
		zap.Int("status_code", statusCode),
	}
	fields = append(fields, additionalFields...)
	
	acl.Error(message, err, fields...)
}

// ValidationLogger 参数验证日志记录器
func (sl *StructuredLogger) LogValidationError(businessContext string, err error, additionalFields ...zap.Field) {
	message := fmt.Sprintf("%s validation failed: %s, error=%s", 
		sl.operation, businessContext, err.Error())
	sl.Error(message, err, additionalFields...)
}

// ConversionLogger 数据转换日志记录器
func (sl *StructuredLogger) LogConversionError(conversionType, businessContext string, err error, additionalFields ...zap.Field) {
	message := fmt.Sprintf("%s %s conversion failed: %s, error=%s", 
		sl.operation, conversionType, businessContext, err.Error())
	sl.Error(message, err, additionalFields...)
}

// 敏感信息脱敏工具函数
func MaskSensitiveData(data string) string {
	if len(data) <= 8 {
		return "***"
	}
	return data[:4] + "***" + data[len(data)-4:]
}

// MaskAccount 脱敏账户信息
func MaskAccount(account string) string {
	return MaskSensitiveData(account)
}

// MaskPixKey 脱敏PIX Key（根据类型决定脱敏程度）
func MaskPixKey(pixKey, keyType string) string {
	switch keyType {
	case "CPF", "CNPJ":
		return MaskSensitiveData(pixKey)
	case "EMAIL":
		// 邮箱特殊处理
		if len(pixKey) > 0 {
			parts := []rune(pixKey)
			for i := 1; i < len(parts)-1; i++ {
				if parts[i] != '@' && parts[i] != '.' {
					parts[i] = '*'
				}
			}
			return string(parts)
		}
		return "***"
	case "PHONE":
		return MaskSensitiveData(pixKey)
	default:
		// 随机key等其他类型
		return MaskSensitiveData(pixKey)
	}
}

// FormatBusinessContext 格式化业务上下文信息
func FormatBusinessContext(params map[string]interface{}) string {
	if len(params) == 0 {
		return ""
	}
	
	var result string
	first := true
	for key, value := range params {
		if !first {
			result += ", "
		}
		result += fmt.Sprintf("%s=%v", key, value)
		first = false
	}
	return result
}

// 常用的业务上下文格式化函数
func FormatPixKeyContext(account, pixKey, keyType string) string {
	return FormatBusinessContext(map[string]interface{}{
		"account":  MaskAccount(account),
		"pix_key":  MaskPixKey(pixKey, keyType),
		"key_type": keyType,
	})
}

func FormatQRCodeContext(txID, pixKey, keyType string, amount float64) string {
	return FormatBusinessContext(map[string]interface{}{
		"tx_id":    txID,
		"pix_key":  MaskPixKey(pixKey, keyType),
		"key_type": keyType,
		"amount":   amount,
	})
}

func FormatPaymentContext(txID, pixKey string, amount float64) string {
	return FormatBusinessContext(map[string]interface{}{
		"tx_id":   txID,
		"pix_key": MaskPixKey(pixKey, ""),
		"amount":  amount,
	})
}
