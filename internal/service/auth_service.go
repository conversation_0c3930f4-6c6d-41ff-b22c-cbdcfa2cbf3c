package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"jdpi-gateway/internal/client"
	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"

	"go.uber.org/zap"
)

// AuthService 认证服务接口
type AuthService interface {
	GetAccessToken(ctx context.Context, requestID string) (*model.AccessToken, error)
	StartTokenRefreshRoutine(ctx context.Context) // 启动定时刷新Token的协程
	StopTokenRefreshRoutine()                     // 停止定时刷新Token的协程
}

// JDAuthService JD认证服务实现
type JDAuthService struct {
	authClient    client.AuthClient
	logger        *zap.Logger
	cache         *tokenCache
	refreshTicker *time.Ticker
	stopRefresh   chan struct{}
	refreshMutex  sync.Mutex
}

// tokenCache 令牌缓存
type tokenCache struct {
	mu    sync.RWMutex
	token *model.AccessToken
}

// NewJDAuthService 创建新的JD认证服务
func NewJDAuthService(authClient client.AuthClient, logger *zap.Logger) *JDAuthService {
	return &JDAuthService{
		authClient:  authClient,
		logger:      logger,
		cache:       &tokenCache{},
		stopRefresh: make(chan struct{}),
	}
}

// GetAccessToken 获取访问令牌
func (s *JDAuthService) GetAccessToken(ctx context.Context, requestID string) (*model.AccessToken, error) {
	// Get trace ID from context
	traceID := interceptor.GetTraceIDFromContext(ctx)
	logger := s.logger.With(
		zap.String("request_id", requestID),
		zap.String("trace_id", traceID),
	)

	logger.Info("Getting access token")

	// 检查缓存中的令牌
	if token := s.getCachedToken(); token != nil && token.IsValid() {
		logger.Info("Using cached access token",
			zap.Time("expires_at", token.ExpiresAt),
			zap.Duration("remaining", time.Until(token.ExpiresAt)),
		)
		return token, nil
	}

	logger.Info("Cached token is invalid or expired, requesting new token")

	// 从认证客户端获取新令牌
	token, err := s.authClient.GetAccessToken(ctx)
	if err != nil {
		logger.Error("Failed to get access token from auth client", zap.Error(err))
		return nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// 缓存新令牌
	s.setCachedToken(token)

	logger.Info("Successfully obtained and cached new access token",
		zap.String("token_type", token.TokenType),
		zap.Int64("expires_in", token.ExpiresIn),
		zap.Time("expires_at", token.ExpiresAt),
	)

	return token, nil
}

// getCachedToken 获取缓存的令牌
func (s *JDAuthService) getCachedToken() *model.AccessToken {
	s.cache.mu.RLock()
	defer s.cache.mu.RUnlock()
	return s.cache.token
}

// setCachedToken 设置缓存的令牌
func (s *JDAuthService) setCachedToken(token *model.AccessToken) {
	s.cache.mu.Lock()
	defer s.cache.mu.Unlock()
	s.cache.token = token
}

// ValidateToken 验证令牌有效性
func (s *JDAuthService) ValidateToken(token *model.AccessToken) error {
	if token == nil {
		return fmt.Errorf("token is nil")
	}

	if token.AccessToken == "" {
		return fmt.Errorf("access token is empty")
	}

	if token.IsExpired() {
		return fmt.Errorf("token is expired")
	}

	return nil
}

// RefreshTokenIfNeeded 如果需要则刷新令牌
func (s *JDAuthService) RefreshTokenIfNeeded(ctx context.Context, requestID string) (*model.AccessToken, error) {
	logger := s.logger.With(zap.String("request_id", requestID))

	token := s.getCachedToken()
	if token == nil || token.IsExpired() {
		logger.Info("Token needs refresh")
		return s.GetAccessToken(ctx, requestID)
	}

	// 如果令牌在5分钟内过期，提前刷新
	if time.Until(token.ExpiresAt) < 5*time.Minute {
		logger.Info("Token expires soon, refreshing proactively",
			zap.Time("expires_at", token.ExpiresAt),
			zap.Duration("remaining", time.Until(token.ExpiresAt)),
		)
		return s.GetAccessToken(ctx, requestID)
	}

	return token, nil
}

// GetTokenInfo 获取令牌信息（用于调试）
func (s *JDAuthService) GetTokenInfo() map[string]interface{} {
	token := s.getCachedToken()
	if token == nil {
		return map[string]interface{}{
			"cached": false,
		}
	}

	return map[string]interface{}{
		"cached":     true,
		"token_type": token.TokenType,
		"scope":      token.Scope,
		"expires_at": token.ExpiresAt,
		"is_valid":   token.IsValid(),
		"is_expired": token.IsExpired(),
		"created_at": token.CreatedAt,
	}
}

// ClearCache 清除缓存（用于测试）
func (s *JDAuthService) ClearCache() {
	s.setCachedToken(nil)
	s.logger.Info("Token cache cleared")
}

// StartTokenRefreshRoutine 启动定时刷新Token的协程
func (s *JDAuthService) StartTokenRefreshRoutine(ctx context.Context) {
	s.refreshMutex.Lock()
	defer s.refreshMutex.Unlock()

	if s.refreshTicker != nil {
		s.logger.Warn("Token refresh routine is already running")
		return
	}

	// 每分钟检查一次Token状态
	s.refreshTicker = time.NewTicker(1 * time.Minute)
	s.logger.Info("Starting token refresh routine")

	go func() {
		defer func() {
			if r := recover(); r != nil {
				s.logger.Error("Token refresh routine panicked", zap.Any("panic", r))
			}
		}()

		for {
			select {
			case <-s.refreshTicker.C:
				s.checkAndRefreshToken(ctx)
			case <-s.stopRefresh:
				s.logger.Info("Token refresh routine stopped")
				return
			case <-ctx.Done():
				s.logger.Info("Token refresh routine stopped due to context cancellation")
				return
			}
		}
	}()
}

// StopTokenRefreshRoutine 停止定时刷新Token的协程
func (s *JDAuthService) StopTokenRefreshRoutine() {
	s.refreshMutex.Lock()
	defer s.refreshMutex.Unlock()

	if s.refreshTicker != nil {
		s.refreshTicker.Stop()
		s.refreshTicker = nil
		close(s.stopRefresh)
		s.stopRefresh = make(chan struct{})
		s.logger.Info("Token refresh routine stopped")
	}
}

// checkAndRefreshToken 检查并刷新Token
func (s *JDAuthService) checkAndRefreshToken(ctx context.Context) {
	token := s.getCachedToken()
	if token == nil {
		s.logger.Debug("No cached token, skipping refresh check")
		return
	}

	// 如果Token在5分钟内过期，提前刷新
	timeUntilExpiry := time.Until(token.ExpiresAt)
	if timeUntilExpiry <= 5*time.Minute {
		s.logger.Info("Token expires soon, refreshing proactively",
			zap.Time("expires_at", token.ExpiresAt),
			zap.Duration("remaining", timeUntilExpiry),
		)

		// 使用新的context来避免原始context被取消的影响
		refreshCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		newToken, err := s.authClient.GetAccessToken(refreshCtx)
		if err != nil {
			s.logger.Error("Failed to refresh access token", zap.Error(err))
			return
		}

		s.setCachedToken(newToken)
		s.logger.Info("Successfully refreshed access token",
			zap.String("token_type", newToken.TokenType),
			zap.Time("expires_at", newToken.ExpiresAt),
		)
	} else {
		s.logger.Debug("Token is still valid, no refresh needed",
			zap.Duration("remaining", timeUntilExpiry),
		)
	}
}
