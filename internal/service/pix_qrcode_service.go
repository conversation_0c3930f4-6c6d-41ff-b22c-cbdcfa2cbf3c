package service

import (
	"context"
	"strconv"

	"jdpi-gateway/internal/client"
	"jdpi-gateway/internal/error_handler"
	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"
	"jdpi-gateway/internal/utils"
	"jdpi-gateway/internal/validator"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	// External protocol imports
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// PixQRCodeService PIX QRCode service interface
type PixQRCodeService interface {
	CreateStaticQRCode(ctx context.Context, req *pixrequest.QRCodeStaticCreateRequest) (*pixresponse.QRCodeStaticCreateResponse, error)
	CreateDynamicQRCode(ctx context.Context, req *pixrequest.QRCodeDynamicCreateRequest) (*pixresponse.QRCodeDynamicCreateResponse, error)
	DecodeQRCodeByPayload(ctx context.Context, req *pixrequest.QRCodeGetByPayloadRequest) (*pixresponse.QRCodeGetByPayloadResponse, error)
	DecodeDynamicQRCodeByUrl(ctx context.Context, req *pixrequest.QRCodeDynamicDecodeByUrlRequest) (*pixresponse.QRCodeDynamicDecodeByUrlResponse, error)
}

// JDPixQRCodeService JD PIX QRCode service implementation
type JDPixQRCodeService struct {
	pixQRCodeClient client.PixQRCodeClient
	authService     AuthService
	validator       *validator.PixQRCodeValidator
	logger          *zap.Logger
}

// NewJDPixQRCodeService creates a new JD PIX QRCode service
func NewJDPixQRCodeService(pixQRCodeClient client.PixQRCodeClient, authService AuthService, logger *zap.Logger) *JDPixQRCodeService {
	return &JDPixQRCodeService{
		pixQRCodeClient: pixQRCodeClient,
		authService:     authService,
		validator:       validator.NewPixQRCodeValidator(),
		logger:          logger,
	}
}

// createExternalLogger creates a logger with trace ID and idempotence ID for external requests
func (s *JDPixQRCodeService) createExternalLogger(ctx context.Context, req interface{}, operation string) *zap.Logger {
	traceID := interceptor.GetTraceIDFromContext(ctx)
	return s.logger.With(
		zap.String("trace_id", traceID),
		zap.String("operation", operation),
		zap.String("component", "JDPixQRCodeService"),
	)
}

// CreateStaticQRCode creates a static QR code using external protocol
func (s *JDPixQRCodeService) CreateStaticQRCode(ctx context.Context, req *pixrequest.QRCodeStaticCreateRequest) (*pixresponse.QRCodeStaticCreateResponse, error) {
	logger := s.createExternalLogger(ctx, req, "CreateStaticQRCode")

	logger.Info("Starting CreateStaticQRCode operation", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidateQRCodeStaticCreateRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeStaticCreateResponse(err), nil
	}

	// Convert external protocol request to JD API request
	var r error = nil
	jdRequest, err := &model.QRCodeStaticCreateJDRequest{
		Format:       utils.ConvertQRCodeResponseTypeToJD(req.Format),
		PixKey:       req.PixKey.KeyValue,
		CategoryCode: utils.GetCategoryCodeForQRCode(),
		Amount:       req.Amount,
		ReceiverName: req.PixKeyOwnerName,
		City:         req.PixKeyCity,
	}, r
	if err != nil {
		logger.Error("Failed to convert request", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeStaticCreateResponse(err), nil
	}

	// Create headers (optional for static QR code creation)
	headers := &model.QRCodeJDHeaders{
		IdempotenceID: req.Header.IdempotenceId,
	}

	// Call JD API
	jdResponse, err := s.pixQRCodeClient.CreateStaticQRCode(ctx, jdRequest, headers)
	if err != nil {
		logger.Error("Failed to create static QR code", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeStaticCreateResponse(err), nil
	}

	logger.Info("Successfully created static QR code")

	// Convert JD response to external protocol response
	return &pixresponse.QRCodeStaticCreateResponse{
		Result: &pixresponse.QRCodeStaticCreateResponse_Response{
			Response: &pixresponse.QRCodeStaticCreateResponseSuccess{
				QrcodeImageBase64:   jdResponse.QRCodeImageBase64,
				QrcodePayloadBase64: jdResponse.QRCodePayloadBase64,
			},
		},
	}, nil
}

// CreateDynamicQRCode creates a dynamic QR code using external protocol
func (s *JDPixQRCodeService) CreateDynamicQRCode(ctx context.Context, req *pixrequest.QRCodeDynamicCreateRequest) (*pixresponse.QRCodeDynamicCreateResponse, error) {
	logger := s.createExternalLogger(ctx, req, "CreateDynamicQRCode")

	logger.Info("Starting CreateDynamicQRCode operation", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidateQRCodeDynamicCreateRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeDynamicCreateResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest, err := s.convertDynamicCreateRequestToJD(req)
	if err != nil {
		logger.Error("Failed to convert request", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeDynamicCreateResponse(err), nil
	}

	// Create headers (optional for dynamic QR code creation)
	headers := &model.QRCodeJDHeaders{
		IdempotenceID: req.Header.IdempotenceId,
	}

	// Call JD API
	jdResponse, err := s.pixQRCodeClient.CreateDynamicQRCode(ctx, jdRequest, headers)
	if err != nil {
		logger.Error("Failed to create dynamic QR code", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeDynamicCreateResponse(err), nil
	}

	logger.Info("Successfully created dynamic QR code", zap.String("document_id", jdResponse.DocumentID))

	// Convert JD response to external protocol response
	return &pixresponse.QRCodeDynamicCreateResponse{
		Result: &pixresponse.QRCodeDynamicCreateResponse_Response{
			Response: &pixresponse.QRCodeDynamicCreateResponseSuccess{
				QrcodeImageBase64:   jdResponse.QRCodeImageBase64,
				QrcodePayloadBase64: jdResponse.QRCodePayloadBase64,
				PayloadJws:          jdResponse.PayloadJWS,
				DocumentId:          jdResponse.DocumentID,
			},
		},
	}, nil
}

// DecodeQRCodeByPayload gets QR code information by payload using external protocol
func (s *JDPixQRCodeService) DecodeQRCodeByPayload(ctx context.Context, req *pixrequest.QRCodeGetByPayloadRequest) (*pixresponse.QRCodeGetByPayloadResponse, error) {
	logger := s.createExternalLogger(ctx, req, "DecodeQRCodeByPayload")

	logger.Info("Starting DecodeQRCodeByPayload operation", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidateQRCodeGetByPayloadRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeGetByPayloadResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest := &model.QRCodeGetByPayloadJDRequest{
		QRCodePayload: req.QrcodePayload,
	}

	// Create headers (required for DecodeQRCodeByPayload)
	headers := &model.QRCodeJDHeaders{
		IdempotenceID: req.Header.IdempotenceId,
		EndToEndID:    req.EndToEndId,
		PayerID:       req.PayerDocumentId,
	}

	// Call JD API
	jdResponse, err := s.pixQRCodeClient.DecodeQRCodeByPayload(ctx, jdRequest, headers)
	if err != nil {
		logger.Error("Failed to get QR code by payload", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeGetByPayloadResponse(err), nil
	}

	logger.Info("Successfully got QR code by payload", zap.String("end_to_end_id", jdResponse.EndToEndID))

	// Convert JD response to external protocol response
	return &pixresponse.QRCodeGetByPayloadResponse{
		Result: &pixresponse.QRCodeGetByPayloadResponse_Response{
			Response: &pixresponse.QRCodeGetByPayloadResponseSuccess{
				EndToEndId:       jdResponse.EndToEndID,
				QrcodeType:       utils.ConvertJDToQRCodeType(jdResponse.QRCodeType),
				StaticQrcodeData: s.convertStaticQRCodeDataJDToExternal(jdResponse.StaticQRCodeData),
			},
		},
	}, nil
}

// DecodeDynamicQRCodeByUrl decodes dynamic QR code by URL using external protocol
func (s *JDPixQRCodeService) DecodeDynamicQRCodeByUrl(ctx context.Context, req *pixrequest.QRCodeDynamicDecodeByUrlRequest) (*pixresponse.QRCodeDynamicDecodeByUrlResponse, error) {
	logger := s.createExternalLogger(ctx, req, "DecodeDynamicQRCodeByUrl")

	logger.Info("Starting DecodeDynamicQRCodeByUrl operation", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidateQRCodeDynamicDecodeByUrlRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeDynamicDecodeByUrlResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest := &model.QRCodeDynamicDecodeByUrlJDRequest{
		URLPayloadJSON: req.QrcodePayloadUrl,
		// CityCode and PresentationDate are optional and not provided in the external request
	}

	// Create headers (required for DecodeDynamicQRCodeByUrl)
	headers := &model.QRCodeJDHeaders{
		IdempotenceID: req.Header.IdempotenceId,
		EndToEndID:    req.EndToEndId,
		PayerID:       req.PayerDocumentId,
	}

	// Call JD API with required headers
	jdResponse, err := s.pixQRCodeClient.DecodeDynamicQRCodeByUrl(ctx, jdRequest, headers)
	if err != nil {
		logger.Error("Failed to decode dynamic QR code by URL", zap.Error(err))
		return error_handler.ConvertJDErrorToQRCodeDynamicDecodeByUrlResponse(err), nil
	}

	logger.Info("Successfully decoded dynamic QR code by URL", zap.String("end_to_end_id", jdResponse.EndToEndID))

	// Convert JD response to external protocol response
	return &pixresponse.QRCodeDynamicDecodeByUrlResponse{
		Result: &pixresponse.QRCodeDynamicDecodeByUrlResponse_Response{
			Response: &pixresponse.QRCodeDynamicDecodeByUrlResponseSuccess{
				EndToEndId:        jdResponse.EndToEndID,
				QrcodeType:        utils.ConvertJDToQRCodeType(jdResponse.QRCodeType),
				DynamicQrcodeData: s.convertDynamicQRCodeDataJDToExternal(jdResponse.DynamicQRCodeData),
			},
		},
	}, nil
}

// convertDynamicCreateRequestToJD converts external QRCodeDynamicCreateRequest to JD API request
func (s *JDPixQRCodeService) convertDynamicCreateRequestToJD(req *pixrequest.QRCodeDynamicCreateRequest) (*model.QRCodeDynamicCreateJDRequest, error) {

	jdRequest := &model.QRCodeDynamicCreateJDRequest{
		Ispb:                     "12345678", // This should come from configuration
		Format:                   utils.ConvertQRCodeResponseTypeToJD(req.Format),
		PixKey:                   req.PixKey.KeyValue,
		CategoryCode:             utils.GetCategoryCodeForQRCode(),
		ReceiverName:             req.PixKeyOwnerName,
		City:                     req.PixKeyCity,
		PostalCode:               "00000000", // Default postal code, should be configurable
		OriginalAmount:           0.01,       // Default amount for dynamic QR codes
		ChangeModality:           0,          // Default change modality
		ReceiverReconciliationID: req.TxId,
		Reusable:                 req.Reusable,
		PayloadJSONURL:           "https://example.com/payload", // Should be configurable
		JWKURL:                   "https://example.com/jwk",     // Should be configurable
	}

	// Set expiration if provided, otherwise use default
	if req.ExpireIn > 0 {
		jdRequest.QRExpiration = int(req.ExpireIn)
	} else {
		jdRequest.QRExpiration = 86400 // Default 24 hours
	}

	// Add payer information if provided
	if req.Payer != nil {
		if req.Payer.HolderType == wallet.AccountHolderType_NATURAL {
			jdRequest.PayerCPF = req.Payer.DocumentId
		} else {
			jdRequest.PayerCNPJ = req.Payer.DocumentId
		}
		jdRequest.PayerName = req.Payer.HolderName
	}

	return jdRequest, nil
}

func (s *JDPixQRCodeService) convertStaticQRCodeDataJDToExternal(data *model.StaticQRCodeDataJD) *wallet.StaticQRCodeData {
	if data == nil {
		return nil
	}

	return &wallet.StaticQRCodeData{
		Amount:                   data.Amount,
		PixKey:                   &wallet.PixKey{KeyValue: data.PixKey},
		PayeeAccount:             &wallet.BankAccount{Ispb: int32(data.Ispb), BranchCode: data.Branch, AccountNumber: data.Account, AccountType: wallet.BankAccountType(data.AccountType)},
		PayeeAccountHolder:       &wallet.BankAccountHolder{HolderType: wallet.AccountHolderType(data.ReceiverPersonType), HolderName: data.ReceiverName, DocumentId: strconv.FormatInt(data.ReceiverCPFCNPJ, 10)},
		City:                     data.City,
		CategoryCode:             data.CategoryCode,
		ReceiverReconciliationId: data.ReceiverReconciliationID,
	}
}

func (s *JDPixQRCodeService) convertDynamicQRCodeDataJDToExternal(data *model.DynamicQRCodeDataJD) *wallet.DynamicQRCodeData {
	if data == nil {
		return nil
	}

	return &wallet.DynamicQRCodeData{
		Revision:              int32(data.Revision),
		PixKey:                &wallet.PixKey{KeyValue: data.PixKey},
		PayeeAccount:          &wallet.BankAccount{Ispb: int32(data.Ispb), BranchCode: data.Branch, AccountNumber: data.Account, AccountType: wallet.BankAccountType(data.AccountType)},
		PayeeAccountHolder:    &wallet.BankAccountHolder{HolderType: wallet.AccountHolderType(data.ReceiverPersonType), HolderName: data.ReceiverName, DocumentId: strconv.FormatInt(data.ReceiverCPFCNPJ, 10)},
		PayeeReconciliationId: data.ReceiverReconciliationID,
		CategoryCode:          data.CategoryCode,
		PayerAccountHolder:    &wallet.BankAccountHolder{HolderType: wallet.AccountHolderType_NATURAL, HolderName: data.PayerName, DocumentId: data.PayerCNPJ},
		PayerRequest:          data.PayerRequest,
		City:                  data.City,
		PostalCode:            data.PostalCode,
		OriginalAmount:        data.OriginalAmount,
		FinalAmount:           data.OriginalAmount, // Using original amount as final amount for now
		QrcodeExpiration:      int32(data.QRExpiration),
		CreationDatetime:      timestamppb.New(data.CreationDateTime),
		PresentationDatetime:  timestamppb.New(data.PresentationDateTime),
		ReceiverPspUrl:        data.ReceiverPSPURL,
		Reusable:              data.Reusable,
		Status:                wallet.QRCodeBillingStatus(data.Status),
	}
}
