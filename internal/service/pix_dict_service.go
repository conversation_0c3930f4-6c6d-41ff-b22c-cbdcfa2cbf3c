package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"jdpi-gateway/internal/client"
	"jdpi-gateway/internal/error_handler"
	"jdpi-gateway/internal/interceptor"
	"jdpi-gateway/internal/model"
	"jdpi-gateway/internal/utils"
	"jdpi-gateway/internal/validator"

	"go.uber.org/zap"

	// External protocol imports
	pixrequest "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/request"
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
	"gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/wallet"
)

// PixDictService PIX Dict service interface
type PixDictService interface {
	CreatePixKey(ctx context.Context, req *pixrequest.PixKeyCreateRequest) (*pixresponse.PixKeyCreateResponse, error)
	UpdatePixKey(ctx context.Context, req *pixrequest.PixKeyUpdateRequest) (*pixresponse.PixKeyUpdateResponse, error)
	GetPixKey(ctx context.Context, req *pixrequest.PixKeyGetRequest) (*pixresponse.PixKeyGetResponse, error)
	VerifyPixKeyExistence(ctx context.Context, req *pixrequest.PixKeyIsExistRequest) (*pixresponse.PixKeyIsExistResponse, error)
	DeletePixKey(ctx context.Context, req *pixrequest.PixKeyDeleteRequest) (*pixresponse.PixKeyDeleteResponse, error)
	ListPixKeys(ctx context.Context, req *pixrequest.PixKeyListByAccountRequest) (*pixresponse.PixKeyListByAccountResponse, error)
	NotifyAccountClosure(ctx context.Context, req *pixrequest.NotifyAccountClosureRequest) (*pixresponse.NotifyAccountClosureResponse, error)
}

// JDPixDictService JD PIX Dict service implementation
type JDPixDictService struct {
	pixDictClient client.PixDictClient
	authService   AuthService
	validator     *validator.PixDictValidator
	logger        *zap.Logger
}

// NewPixDictService creates a new PIX Dict service
func NewPixDictService(pixDictClient client.PixDictClient, authService AuthService, logger *zap.Logger) *JDPixDictService {

	return &JDPixDictService{
		pixDictClient: pixDictClient,
		authService:   authService,
		validator:     validator.NewPixDictValidator(),
		logger:        logger,
	}
}

// createExternalLogger creates a logger for external protocol methods
func (s *JDPixDictService) createExternalLogger(ctx context.Context, req interface{}, operation string) *zap.Logger {
	traceID := interceptor.GetTraceIDFromContext(ctx)

	// Safe access to idempotence_id
	var idempotenceID string
	if req != nil {
		// Try to access Header.IdempotenceId directly for known types
		switch v := req.(type) {
		case *pixrequest.PixKeyCreateRequest:
			if v.Header != nil {
				idempotenceID = v.Header.IdempotenceId
			}
		case *pixrequest.PixKeyDeleteRequest:
			if v.Header != nil {
				idempotenceID = v.Header.IdempotenceId
			}
		case *pixrequest.PixKeyUpdateRequest:
			if v.Header != nil {
				idempotenceID = v.Header.IdempotenceId
			}
		case *pixrequest.PixKeyListByAccountRequest:
			if v.Header != nil {
				idempotenceID = v.Header.IdempotenceId
			}
		case *pixrequest.PixKeyIsExistRequest:
			if v.Header != nil {
				idempotenceID = v.Header.IdempotenceId
			}
		case *pixrequest.PixKeyGetRequest:
			if v.Header != nil {
				idempotenceID = v.Header.IdempotenceId
			}
		case *pixrequest.NotifyAccountClosureRequest:
			if v.Header != nil {
				idempotenceID = v.Header.IdempotenceId
			}
		}
	}

	return s.logger.With(
		zap.String("idempotence_id", idempotenceID),
		zap.String("trace_id", traceID),
		zap.String("operation", operation),
	)
}

// CreatePixKey creates a PIX key using external protocol
func (s *JDPixDictService) CreatePixKey(ctx context.Context, req *pixrequest.PixKeyCreateRequest) (*pixresponse.PixKeyCreateResponse, error) {
	logger := s.createExternalLogger(ctx, req, "CreatePixKey")

	logger.Info("Starting CreatePixKey operation: ", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidatePixKeyCreateRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToCreatePixKeyResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest, err := s.convertExternalToJDRequest(req)
	if err != nil {
		logger.Error("Failed to convert request", zap.Error(err))
		return error_handler.ConvertJDErrorToCreatePixKeyResponse(err), nil
	}

	// Call JD API
	jdResponse, err := s.pixDictClient.CreatePixKey(ctx, jdRequest, req.Header.IdempotenceId)
	if err != nil {
		logger.Error("Failed to create PIX key", zap.Error(err))
		return error_handler.ConvertJDErrorToCreatePixKeyResponse(err), nil
	}

	logger.Info(fmt.Sprintf("Successfully created PIX key: Account=%s, PixKey=%s", jdRequest.Account, jdResponse.PixKey))

	// Convert JD response to external protocol response
	return s.convertJDToExternalResponse(jdResponse, req.PixKey.KeyType)
}

// UpdatePixKey updates a PIX key using external protocol
func (s *JDPixDictService) UpdatePixKey(ctx context.Context, req *pixrequest.PixKeyUpdateRequest) (*pixresponse.PixKeyUpdateResponse, error) {
	logger := s.createExternalLogger(ctx, req, "UpdatePixKey")

	logger.Info("Starting UpdatePixKey operation: ", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidatePixKeyUpdateRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToUpdatePixKeyResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest := &model.UpdatePixKeyJDRequest{
		PixKey:                    req.PixKey.KeyValue,
		Ispb:                      int(req.Ispb),
		NewBranch:                 req.BankAccount.BranchCode,
		NewAccountType:            int(req.BankAccount.AccountType),
		NewAccountNumber:          req.BankAccount.AccountNumber,
		NewAccountOpeningDatetime: req.BankAccount.AccountOpeningDatetime.AsTime().Format("2006-01-02T15:04:05"),
		NewName:                   req.BankAccountHolder.HolderName,
		NewTradeName:              req.BankAccountHolder.HolderNickname,
		UpdateReason:              int(req.Reason),
	}

	// Call JD API directly
	jdResponse, err := s.pixDictClient.UpdatePixKey(ctx, jdRequest, req.Header.IdempotenceId)
	if err != nil {
		logger.Error("Failed to update PIX key", zap.Error(err))
		return error_handler.ConvertJDErrorToUpdatePixKeyResponse(err), nil
	}

	logger.Info(fmt.Sprintf("Successfully updated PIX key: %s", jdResponse.PixKey))

	// Return success response
	return &pixresponse.PixKeyUpdateResponse{
		Result: &pixresponse.PixKeyUpdateResponse_Response{
			Response: &pixresponse.PixKeyUpdateResponseSuccess{
				PixKey: &wallet.PixKey{
					KeyType:  req.PixKey.KeyType,
					KeyValue: jdResponse.PixKey,
				},
				KeyCreationDatetime:        utils.ParseTimestampSafe(jdResponse.PixKeyCreationDatetime),
				KeyPossessionStartDatetime: utils.ParseTimestampSafe(jdResponse.PixKeyOwnershipStartDatetime),
			},
		},
	}, nil
}

// GetPixKey gets PIX key information using external protocol
func (s *JDPixDictService) GetPixKey(ctx context.Context, req *pixrequest.PixKeyGetRequest) (*pixresponse.PixKeyGetResponse, error) {
	logger := s.createExternalLogger(ctx, req, "GetPixKey")

	logger.Info("Starting GetPixKey operation: ", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidatePixKeyGetRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToGetPixKeyResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest := &model.QueryPixKeyJDRequest{
		EndToEndId: req.E2EId,
		PlayerId:   req.RequesterDocumentId,
		PixKey:     req.PixKey.KeyValue,
	}

	// Call JD API directly
	jdResponse, err := s.pixDictClient.QueryPixKey(ctx, jdRequest, req.Header.IdempotenceId)
	if err != nil {
		logger.Error("Failed to get PIX key", zap.Error(err))
		return error_handler.ConvertJDErrorToGetPixKeyResponse(err), nil
	}

	logger.Info(fmt.Sprintf("Successfully got PIX key: %s", jdResponse.PixKey))

	// Convert response
	var pixKey *wallet.PixKey
	var bankAccount *wallet.BankAccount
	var bankAccountHolder *wallet.BankAccountHolder

	if jdResponse != nil {
		// Build PixKey
		pixKey = &wallet.PixKey{
			KeyType:  wallet.PixKeyType(jdResponse.PixKeyType),
			KeyValue: jdResponse.PixKey,
		}

		// Build BankAccount
		bankAccount = &wallet.BankAccount{
			Ispb:                   int32(jdResponse.Ispb),
			BranchCode:             jdResponse.Branch,
			AccountNumber:          jdResponse.Account,
			AccountType:            wallet.BankAccountType(jdResponse.AccountType),
			Status:                 "ACTIVE", // Default status
			AccountOpeningDatetime: utils.ParseTimestampSafe(jdResponse.AccountOpeningDatetime),
		}

		// Build BankAccountHolder
		bankAccountHolder = &wallet.BankAccountHolder{
			HolderType:     wallet.AccountHolderType(jdResponse.PersonType),
			HolderName:     jdResponse.Name,
			HolderNickname: jdResponse.TradeName,
			DocumentId:     strconv.FormatInt(jdResponse.Document, 10),
		}
	}

	return &pixresponse.PixKeyGetResponse{
		Result: &pixresponse.PixKeyGetResponse_Response{
			Response: &pixresponse.PixKeyGetResponseSuccess{
				PixKey:            pixKey,
				BankAccount:       bankAccount,
				BankAccountHolder: bankAccountHolder,
			},
		},
	}, nil
}

// VerifyPixKeyExistence checks if PIX keys exist using external protocol
func (s *JDPixDictService) VerifyPixKeyExistence(ctx context.Context, req *pixrequest.PixKeyIsExistRequest) (*pixresponse.PixKeyIsExistResponse, error) {
	logger := s.createExternalLogger(ctx, req, "VerifyPixKeyExistence")

	logger.Info("Starting VerifyPixKeyExistence operation: ", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidatePixKeyIsExistRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertToVerifyPixKeyExistenceErrorResponse(err), nil
	}

	// Convert external protocol request to JD API request
	var pixKeysToVerify []model.PixKeyToVerifyJDDto
	keyTypeMapping := make(map[string]wallet.PixKeyType) // Map to track key types from original request

	for _, key := range req.Keys {
		pixKeysToVerify = append(pixKeysToVerify, model.PixKeyToVerifyJDDto{
			PixKey: key.KeyValue,
		})
		keyTypeMapping[key.KeyValue] = key.KeyType
	}

	jdRequest := &model.VerifyPixKeyExistenceJDRequest{
		Ispb:            int(req.Ispb),
		PixKeysToVerify: pixKeysToVerify,
	}

	// Call JD API directly
	jdResponse, err := s.pixDictClient.VerifyPixKeyExistence(ctx, jdRequest, req.Header.IdempotenceId)
	if err != nil {
		logger.Error("Failed to verify PIX key existence", zap.Error(err))
		return error_handler.ConvertToVerifyPixKeyExistenceErrorResponse(err), nil
	}

	logger.Info(fmt.Sprintf("Successfully verified PIX key existence: %d", len(jdResponse.VerifiedPixKeys)))

	// Convert response
	var existentKeys []*wallet.PixKey
	if jdResponse != nil && jdResponse.VerifiedPixKeys != nil {
		for _, key := range jdResponse.VerifiedPixKeys {
			if key.ExistsInDict {
				// Get the key type from the original request mapping
				keyType, exists := keyTypeMapping[key.PixKey]
				if !exists {
					keyType = wallet.PixKeyType_EVP // Fallback to default type
				}

				existentKeys = append(existentKeys, &wallet.PixKey{
					KeyType:  keyType,
					KeyValue: key.PixKey,
				})
			}
		}
	}

	return &pixresponse.PixKeyIsExistResponse{
		Result: &pixresponse.PixKeyIsExistResponse_Response{
			Response: &pixresponse.PixKeyIsExistResponseSuccess{
				ExistentPixKey: existentKeys,
			},
		},
	}, nil
}

// DeletePixKey deletes a PIX key using external protocol
func (s *JDPixDictService) DeletePixKey(ctx context.Context, req *pixrequest.PixKeyDeleteRequest) (*pixresponse.PixKeyDeleteResponse, error) {
	logger := s.createExternalLogger(ctx, req, "DeletePixKey")

	logger.Info("Starting DeletePixKey operation: ", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidatePixKeyDeleteRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToDeletePixKeyResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest := &model.DeletePixKeyJDRequest{
		PixKey: req.PixKey.KeyValue,
		Ispb:   int(req.Ispb),
		Reason: int(req.Reason),
	}

	// Call JD API directly
	jdResponse, err := s.pixDictClient.DeletePixKey(ctx, jdRequest, req.Header.IdempotenceId)
	if err != nil {
		logger.Error("Failed to delete PIX key", zap.Error(err))
		return error_handler.ConvertJDErrorToDeletePixKeyResponse(err), nil
	}

	logger.Info(fmt.Sprintf("Successfully deleted PIX key: %s", jdResponse.PixKey))

	// Return success response
	return &pixresponse.PixKeyDeleteResponse{
		Result: &pixresponse.PixKeyDeleteResponse_Response{
			Response: &pixresponse.PixKeyDeleteResponseSuccess{
				PixKey: &wallet.PixKey{
					KeyType:  req.PixKey.KeyType,
					KeyValue: jdResponse.PixKey,
				},
			},
		},
	}, nil
}

// ListPixKeys lists PIX keys by account using external protocol
func (s *JDPixDictService) ListPixKeys(ctx context.Context, req *pixrequest.PixKeyListByAccountRequest) (*pixresponse.PixKeyListByAccountResponse, error) {
	logger := s.createExternalLogger(ctx, req, "ListPixKeys")

	logger.Info("Starting ListPixKeys operation: ", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidatePixKeyListByAccountRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToListPixKeysResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest := &model.ListPixKeysJDRequest{
		Ispb:        int(req.Ispb),
		Branch:      req.BankAccount.BranchCode,
		AccountType: int(req.BankAccount.AccountType),
		Account:     req.BankAccount.AccountNumber,
		PersonType:  int(req.BankAccountHolder.HolderType),
		Document:    utils.ParseDocumentID(req.BankAccountHolder.DocumentId),
	}

	// Call JD API directly
	jdResponse, err := s.pixDictClient.ListPixKeys(ctx, jdRequest, req.Header.IdempotenceId)
	if err != nil {
		logger.Error("Failed to list PIX keys", zap.Error(err))
		return error_handler.ConvertJDErrorToListPixKeysResponse(err), nil
	}

	// Convert response
	var pixKeys []*wallet.PixKey
	if jdResponse != nil && jdResponse.PixKeys != nil {
		for _, key := range jdResponse.PixKeys {
			pixKeys = append(pixKeys, &wallet.PixKey{
				KeyType:  utils.ConvertJDPixKeyTypeToEnum(key.PixKeyType),
				KeyValue: key.PixKey,
			})
		}
	}

	logger.Info(fmt.Sprintf("Successfully listed PIX keys: %d", len(pixKeys)))

	return &pixresponse.PixKeyListByAccountResponse{
		Result: &pixresponse.PixKeyListByAccountResponse_Response{
			Response: &pixresponse.PixKeyListByAccountResponseSuccess{
				PixKey: pixKeys,
			},
		},
	}, nil
}

// NotifyAccountClosure notifies account closure using external protocol
func (s *JDPixDictService) NotifyAccountClosure(ctx context.Context, req *pixrequest.NotifyAccountClosureRequest) (*pixresponse.NotifyAccountClosureResponse, error) {
	logger := s.createExternalLogger(ctx, req, "NotifyAccountClosure")

	logger.Info("Starting NotifyAccountClosure operation: ", zap.Any("request", req))

	// Validate request using validator
	if err := s.validator.ValidateNotifyAccountClosureRequest(req); err != nil {
		logger.Error("Invalid request", zap.Error(err))
		return error_handler.ConvertJDErrorToNotifyAccountClosureResponse(err), nil
	}

	// Convert external protocol request to JD API request
	jdRequest := &model.NotifyAccountClosureJDRequest{
		Ispb:        int(req.Ispb),
		Branch:      req.BankAccount.BranchCode,
		AccountType: int(req.BankAccount.AccountType),
		Account:     req.BankAccount.AccountNumber,
		PersonType:  1, // Default person type, should be extracted from request if available
		Document:    0, // Default document, should be extracted from request if available
		Reason:      1, // Default reason, should be extracted from request if available
	}

	// Call JD API directly
	_, err := s.pixDictClient.NotifyAccountClosure(ctx, jdRequest, req.Header.IdempotenceId)
	if err != nil {
		logger.Error("Failed to notify account closure", zap.Error(err))
		return error_handler.ConvertJDErrorToNotifyAccountClosureResponse(err), nil
	}

	logger.Info(fmt.Sprintf("Successfully notified account closure: %s", req.BankAccount.AccountNumber))

	// Return success response
	return &pixresponse.NotifyAccountClosureResponse{
		Result: &pixresponse.NotifyAccountClosureResponse_Response{
			Response: &pixresponse.NotifyAccountClosureResponseSuccess{},
		},
	}, nil
}

// convertExternalToJDRequest converts external PixKeyCreateRequest to JD API request
func (s *JDPixDictService) convertExternalToJDRequest(req *pixrequest.PixKeyCreateRequest) (*model.CreatePixKeyJDRequest, error) {
	if req == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	// Parse document ID to int64
	documentID := utils.ParseDocumentID(req.BankAccountHolder.DocumentId)

	// Parse account opening datetime
	var accountOpeningDatetime string
	if req.BankAccount.AccountOpeningDatetime != nil {
		accountOpeningDatetime = req.BankAccount.AccountOpeningDatetime.AsTime().Format("2006-01-02T15:04:05.000Z")
	} else {
		accountOpeningDatetime = time.Now().Format("2006-01-02T15:04:05.000Z")
	}

	return &model.CreatePixKeyJDRequest{
		PixKeyType:             int(req.PixKey.KeyType),
		PixKey:                 req.PixKey.KeyValue,
		Ispb:                   int(req.Ispb),
		Branch:                 req.BankAccount.BranchCode,
		AccountType:            int(req.BankAccount.AccountType),
		Account:                req.BankAccount.AccountNumber,
		AccountOpeningDatetime: accountOpeningDatetime,
		PersonType:             int(req.BankAccountHolder.HolderType),
		Document:               documentID,
		Name:                   req.BankAccountHolder.HolderName,
		TradeName:              req.BankAccountHolder.HolderNickname,
		Reason:                 int(req.Reason),
	}, nil
}

// convertJDToExternalResponse converts JD API response to external PixKeyCreateResponse
func (s *JDPixDictService) convertJDToExternalResponse(jdResponse *model.CreatePixKeyJDResponse, keyType wallet.PixKeyType) (*pixresponse.PixKeyCreateResponse, error) {
	if jdResponse == nil {
		return &pixresponse.PixKeyCreateResponse{
			Result: &pixresponse.PixKeyCreateResponse_Error{
				Error: &pixresponse.Error{
					Error:        pixresponse.ErrorCode_INTERNAL_ERROR,
					ErrorMessage: "JD API response is nil",
				},
			},
		}, nil
	}

	// Parse timestamps
	keyCreationTime := utils.ParseTimestampSafe(jdResponse.PixKeyCreationDatetime)
	keyPossessionTime := utils.ParseTimestampSafe(jdResponse.PixKeyOwnershipStartDatetime)

	return &pixresponse.PixKeyCreateResponse{
		Result: &pixresponse.PixKeyCreateResponse_Response{
			Response: &pixresponse.PixKeyCreateResponseSuccess{
				PixKey: &wallet.PixKey{
					KeyType:  keyType,
					KeyValue: jdResponse.PixKey,
				},
				KeyCreationDatetime:        keyCreationTime,
				KeyPossessionStartDatetime: keyPossessionTime,
			},
		},
	}, nil
}
