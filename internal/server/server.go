package server

import (
	"context"
	"fmt"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"jdpi-gateway/config"
	"jdpi-gateway/internal/handler"
	"jdpi-gateway/internal/interceptor"
	pb "jdpi-gateway/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	// Protocol imports
	pixservice "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix"
)

// Server Defines the gRPC server
type Server struct {
	config            *config.Config
	logger            *zap.Logger
	grpcServer        *grpc.Server
	authHandler       *handler.AuthHandler
	pixDictHandler    *handler.PixDictHandler
	pixQRCodeHandler  *handler.PixQRCodeHandler
	pixPaymentHandler *handler.PixPaymentHandler
}

// NewServer Creates a new gRPC server
func NewServer(cfg *config.Config, logger *zap.Logger, authHandler *handler.<PERSON><PERSON><PERSON><PERSON><PERSON>, pixDictHandler *handler.<PERSON>x<PERSON><PERSON><PERSON><PERSON><PERSON>, pixQRCodeHandler *handler.PixQRCodeHandler, pixPaymentHandler *handler.PixPaymentHandler) *Server {
	return &Server{
		config:            cfg,
		logger:            logger,
		authHandler:       authHandler,
		pixDictHandler:    pixDictHandler,
		pixQRCodeHandler:  pixQRCodeHandler,
		pixPaymentHandler: pixPaymentHandler,
	}
}

// Start the server
func (s *Server) Start(version string) error {
	// Create listener
	addr := fmt.Sprintf("%s:%d", s.config.Server.Host, s.config.Server.Port)
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", addr, err)
	}

	// Create gRPC server options
	opts := []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(
			interceptor.RecoveryInterceptor(s.logger),
			interceptor.TraceIDInterceptor(s.logger),
			interceptor.ValidationInterceptor(s.logger),
			interceptor.LoggerInterceptor(s.logger),
		),
		grpc.ChainStreamInterceptor(
			interceptor.StreamRecoveryInterceptor(s.logger),
			interceptor.StreamTraceIDInterceptor(s.logger),
			interceptor.StreamLoggerInterceptor(s.logger),
		),
	}

	// Create gRPC server
	s.grpcServer = grpc.NewServer(opts...)

	// Register services
	pb.RegisterAuthServiceServer(s.grpcServer, s.authHandler)
	pixservice.RegisterPixDictServiceServer(s.grpcServer, s.pixDictHandler)
	pixservice.RegisterPixQRCodeServiceServer(s.grpcServer, s.pixQRCodeHandler)
	pixservice.RegisterPixTransactionServiceServer(s.grpcServer, s.pixPaymentHandler)

	// Enable reflection (for debugging, can be disabled in production)
	reflection.Register(s.grpcServer)

	s.logger.Info("Starting gRPC server",
		zap.String("address", addr),
		zap.String("version", version),
	)

	// Start server
	go func() {
		if err := s.grpcServer.Serve(listener); err != nil {
			s.logger.Fatal("Failed to serve gRPC server", zap.Error(err))
		}
	}()

	s.logger.Info("gRPC server started successfully", zap.String("address", addr))

	// Wait for interrupt signal
	s.waitForShutdown()

	return nil
}

// Stop the server
func (s *Server) Stop() {
	if s.grpcServer != nil {
		s.logger.Info("Shutting down gRPC server...")

		// Graceful shutdown
		done := make(chan struct{})
		go func() {
			s.grpcServer.GracefulStop()
			close(done)
		}()

		// Wait for graceful shutdown or timeout
		select {
		case <-done:
			s.logger.Info("gRPC server stopped gracefully")
		case <-time.After(30 * time.Second):
			s.logger.Warn("Graceful shutdown timeout, forcing stop")
			s.grpcServer.Stop()
		}
	}
}

// Waits for shutdown signal
func (s *Server) waitForShutdown() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	sig := <-sigChan
	s.logger.Info("Received shutdown signal", zap.String("signal", sig.String()))

	s.Stop()
}

// Health check
func (s *Server) Health(ctx context.Context) error {
	// Check gRPC server status
	if s.grpcServer == nil {
		return fmt.Errorf("gRPC server is not initialized")
	}

	// Check handler health status
	if err := s.authHandler.Health(ctx); err != nil {
		return fmt.Errorf("handler health check failed: %w", err)
	}

	return nil
}

// GetServerInfo retrieves server information
func (s *Server) GetServerInfo() map[string]interface{} {
	info := map[string]interface{}{
		"server": map[string]interface{}{
			"host":    s.config.Server.Host,
			"port":    s.config.Server.Port,
			"version": "1.0.0",
		},
		"jd": map[string]interface{}{
			"base_url":   s.config.JD.BaseURL,
			"grant_type": s.config.JD.GrantType,
			"scope":      s.config.JD.Scope,
			"mock_mode":  s.config.JD.MockMode,
		},
	}

	// Add token information
	if tokenInfo := s.authHandler.GetTokenInfo(); tokenInfo != nil {
		info["token"] = tokenInfo
	}

	return info
}
