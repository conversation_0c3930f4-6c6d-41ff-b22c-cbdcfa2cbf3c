package error_handler

import pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"

// ConvertJDErrorToCreatePixKeyResponse converts JD API error to external error response
func ConvertJDErrorToCreatePixKeyResponse(err error) *pixresponse.PixKeyCreateResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.PixKeyCreateResponse { return &pixresponse.PixKeyCreateResponse{} },
		func(resp *pixresponse.PixKeyCreateResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.PixKeyCreateResponse_Error{Error: err}
		},
	)
}

// ConvertJDErrorToDeletePixKeyResponse converts JD API error to external delete error response
func ConvertJDErrorToDeletePixKeyResponse(err error) *pixresponse.PixKeyDeleteResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.PixKeyDeleteResponse { return &pixresponse.PixKeyDeleteResponse{} },
		func(resp *pixresponse.PixKeyDeleteResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.PixKeyDeleteResponse_Error{Error: err}
		},
	)
}

// ConvertJDErrorToUpdatePixKeyResponse converts JD API error to external update error response
func ConvertJDErrorToUpdatePixKeyResponse(err error) *pixresponse.PixKeyUpdateResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.PixKeyUpdateResponse { return &pixresponse.PixKeyUpdateResponse{} },
		func(resp *pixresponse.PixKeyUpdateResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.PixKeyUpdateResponse_Error{Error: err}
		},
	)
}

// ConvertJDErrorToListPixKeysResponse converts JD API error to external list error response
func ConvertJDErrorToListPixKeysResponse(err error) *pixresponse.PixKeyListByAccountResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.PixKeyListByAccountResponse { return &pixresponse.PixKeyListByAccountResponse{} },
		func(resp *pixresponse.PixKeyListByAccountResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.PixKeyListByAccountResponse_Error{Error: err}
		},
	)
}

// ConvertToVerifyPixKeyExistenceErrorResponse converts JD API error to external is exist error response
func ConvertToVerifyPixKeyExistenceErrorResponse(err error) *pixresponse.PixKeyIsExistResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.PixKeyIsExistResponse { return &pixresponse.PixKeyIsExistResponse{} },
		func(resp *pixresponse.PixKeyIsExistResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.PixKeyIsExistResponse_Error{Error: err}
		},
	)
}

// ConvertJDErrorToGetPixKeyResponse converts JD API error to external get error response
func ConvertJDErrorToGetPixKeyResponse(err error) *pixresponse.PixKeyGetResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.PixKeyGetResponse { return &pixresponse.PixKeyGetResponse{} },
		func(resp *pixresponse.PixKeyGetResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.PixKeyGetResponse_Error{Error: err}
		},
	)
}

// ConvertJDErrorToNotifyAccountClosureResponse converts JD API error to external notify error response
func ConvertJDErrorToNotifyAccountClosureResponse(err error) *pixresponse.NotifyAccountClosureResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.NotifyAccountClosureResponse { return &pixresponse.NotifyAccountClosureResponse{} },
		func(resp *pixresponse.NotifyAccountClosureResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.NotifyAccountClosureResponse_Error{Error: err}
		},
	)
}
