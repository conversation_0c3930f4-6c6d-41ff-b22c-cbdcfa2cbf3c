package error_handler

import (
	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
)

// ConvertToPixTransactionGetErrorResponse converts JD API error to PixTransactionGet response
func ConvertToPixTransactionGetErrorResponse(err error) *pixresponse.PixTransactionGetResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.PixTransactionGetResponse { return &pixresponse.PixTransactionGetResponse{} },
		func(resp *pixresponse.PixTransactionGetResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.PixTransactionGetResponse_Error{Error: err}
		},
	)
}

// ConvertToPixOutConfirmErrorResponse creates validation error response for PixOutConfirm
func ConvertToPixOutConfirmErrorResponse(err error) *pixresponse.PixOutConfirmResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.PixOutConfirmResponse { return &pixresponse.PixOutConfirmResponse{} },
		func(resp *pixresponse.PixOutConfirmResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.PixOutConfirmResponse_Error{Error: err}
		},
	)
}
