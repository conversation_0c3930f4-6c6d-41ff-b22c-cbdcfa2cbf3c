package error_handler

import (
	"fmt"

	"jdpi-gateway/internal/model"
	"jdpi-gateway/internal/validator"

	pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"
)

// ConvertErrorToCommonError converts JD API error to commonResponse.proto Error structure
func ConvertErrorToCommonError(err error) *pixresponse.Error {
	// Check if it's a validation error
	if validationErr, ok := err.(*validator.ValidationErrors); ok {
		errorDetails := validationErr.Error()
		errorMessage := fmt.Sprintf("Parameter validation failed: %d parameter validation errors", len(validationErr.Errors))

		return &pixresponse.Error{
			Error:        pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST,
			ErrorSubcode: "INVALID_PARAMETERS",
			ErrorDetails: errorDetails,
			ErrorMessage: errorMessage,
		}
	}

	// Check if it's a JD API error
	if jdAPIErr, ok := err.(*model.JDAPIError); ok {
		// Map JD API error code to commonResponse ErrorCode
		var errorCode pixresponse.ErrorCode
		switch jdAPIErr.StatusCode {
		case 400:
			errorCode = pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST
		case 401, 403:
			errorCode = pixresponse.ErrorCode_ERROR_CHANNEL_REQUEST
		case 500, 502, 503:
			errorCode = pixresponse.ErrorCode_ERROR_CHANNEL_BUSINESS
		default:
			errorCode = pixresponse.ErrorCode_INTERNAL_ERROR
		}

		// Build error details with status code
		errorMessage := fmt.Sprintf("JD API failed, status code: %d", jdAPIErr.StatusCode)

		return &pixresponse.Error{
			Error:        errorCode,
			ErrorSubcode: jdAPIErr.JDError.Codigo,
			ErrorDetails: jdAPIErr.JDError.Mensagem,
			ErrorMessage: errorMessage,
		}
	}

	// For other types of errors, return generic internal error
	return &pixresponse.Error{
		Error:        pixresponse.ErrorCode_INTERNAL_ERROR,
		ErrorSubcode: "INTERNAL_SERVER_ERROR",
		ErrorDetails: err.Error(),
		ErrorMessage: "Internal error occurred",
	}
}

// ConvertToErrorResponseGeneric is a generic function to convert JD API error to external error response
// T must be a response type that can have its Result field set
func ConvertToErrorResponseGeneric[T any](err error, newResponse func() T, setErrorResult func(T, *pixresponse.Error)) T {
	// Convert JD API error to common error structure
	commonError := ConvertErrorToCommonError(err)

	// Create new response instance
	response := newResponse()

	// Set error result
	setErrorResult(response, commonError)

	return response
}
