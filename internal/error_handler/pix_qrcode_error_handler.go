package error_handler

import pixresponse "gitlab.pagsmile.com/wallet/ew_pix_adapter_proto/gen/go/services/pix/response"

// ConvertJDErrorToQRCodeStaticCreateResponse converts JD API error to CreateStaticQRCode error response
func ConvertJDErrorToQRCodeStaticCreateResponse(err error) *pixresponse.QRCodeStaticCreateResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.QRCodeStaticCreateResponse { return &pixresponse.QRCodeStaticCreateResponse{} },
		func(resp *pixresponse.QRCodeStaticCreateResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.QRCodeStaticCreateResponse_Error{Error: err}
		},
	)
}

// ConvertJDErrorToQRCodeDynamicCreateResponse converts JD API error to CreateDynamicQRCode error response
func ConvertJDErrorToQRCodeDynamicCreateResponse(err error) *pixresponse.QRCodeDynamicCreateResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.QRCodeDynamicCreateResponse { return &pixresponse.QRCodeDynamicCreateResponse{} },
		func(resp *pixresponse.QRCodeDynamicCreateResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.QRCodeDynamicCreateResponse_Error{Error: err}
		},
	)
}

// ConvertJDErrorToQRCodeGetByPayloadResponse converts JD API error to DecodeQRCodeByPayload error response
func ConvertJDErrorToQRCodeGetByPayloadResponse(err error) *pixresponse.QRCodeGetByPayloadResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.QRCodeGetByPayloadResponse { return &pixresponse.QRCodeGetByPayloadResponse{} },
		func(resp *pixresponse.QRCodeGetByPayloadResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.QRCodeGetByPayloadResponse_Error{Error: err}
		},
	)
}

// ConvertJDErrorToQRCodeDynamicDecodeByUrlResponse converts JD API error to DecodeDynamicQRCodeByUrl error response
func ConvertJDErrorToQRCodeDynamicDecodeByUrlResponse(err error) *pixresponse.QRCodeDynamicDecodeByUrlResponse {
	return ConvertToErrorResponseGeneric(
		err,
		func() *pixresponse.QRCodeDynamicDecodeByUrlResponse {
			return &pixresponse.QRCodeDynamicDecodeByUrlResponse{}
		},
		func(resp *pixresponse.QRCodeDynamicDecodeByUrlResponse, err *pixresponse.Error) {
			resp.Result = &pixresponse.QRCodeDynamicDecodeByUrlResponse_Error{Error: err}
		},
	)
}
